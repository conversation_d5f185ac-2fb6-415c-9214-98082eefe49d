#!/usr/bin/env node

/**
 * Arabic Translation Inspector for VendorHub
 * 
 * This script comprehensively inspects the entire codebase to ensure
 * complete Arabic translation coverage and RTL component usage.
 * 
 * Usage: node scripts/arabic-translation-inspector.js
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Configuration
const CONFIG = {
  srcDir: path.join(__dirname, '..', 'src'),
  i18nServicePath: path.join(__dirname, '..', 'src', 'services', 'I18nService.ts'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /build/,
    /dist/,
    /coverage/,
    /\.test\./,
    /\.spec\./
  ],
  screenDirectories: [
    'src/screens/admin',
    'src/screens/auth',
    'src/screens/chat',
    'src/screens/public',
    'src/screens/vendor'
  ],
  componentDirectories: [
    'src/components',
    'src/components/RTL'
  ],
  // Portal-specific configuration
  portals: {
    customer: {
      name: 'Customer Portal',
      screens: [
        'src/screens/public/HomeScreen.tsx',
        'src/screens/public/ShopsScreen.tsx',
        'src/screens/public/CartScreen.tsx',
        'src/screens/public/ProductDetailsScreen.tsx',
        'src/screens/public/CheckoutScreen.tsx',
        'src/screens/public/VendorShopScreen.tsx',
        'src/screens/public/SearchScreen.tsx',
        'src/screens/public/ProfileScreen.tsx',
        'src/screens/public/OrderHistoryScreen.tsx',
        'src/screens/public/OrderDetailsScreen.tsx'
      ],
      navigation: 'src/navigation/CustomerNavigator.tsx',
      priority: 'high'
    },
    vendor: {
      name: 'Vendor Portal',
      screens: [
        'src/screens/vendor/VendorDashboardScreen.tsx',
        'src/screens/vendor/VendorPendingScreen.tsx',
        'src/screens/vendor/VendorProductsScreen.tsx',
        'src/screens/vendor/VendorOrdersScreen.tsx',
        'src/screens/vendor/AddProductScreen.tsx',
        'src/screens/vendor/EditProductScreen.tsx',
        'src/screens/vendor/ShopSettingsScreen.tsx'
      ],
      navigation: 'src/navigation/VendorNavigator.tsx',
      priority: 'high'
    }
  }
};

// RTL components that should be used instead of basic React Native components
const RTL_COMPONENT_MAPPING = {
  'View': 'RTLView',
  'Text': 'RTLText',
  'SafeAreaView': 'RTLSafeAreaView',
  'ScrollView': 'RTLScrollView',
  'FlatList': 'RTLFlatList',
  'SectionList': 'RTLSectionList',
  'TextInput': 'RTLInput',
  'Ionicons': 'RTLIcon'
};

// Translation patterns to detect
const TRANSLATION_PATTERNS = {
  useI18n: /useI18n\(\)/g,
  tFunction: /\bt\(['"`]([^'"`]+)['"`]\)/g,
  hardcodedText: /['"`]([A-Za-z][^'"`]*[A-Za-z])['"]/g,
  rtlImport: /import.*\{.*RTL.*\}.*from.*RTL/g,
  basicComponentUsage: /<(View|Text|SafeAreaView|ScrollView|FlatList|SectionList|TextInput)\s/g
};

class ArabicTranslationInspector {
  constructor() {
    this.results = {
      screens: [],
      components: [],
      translations: {
        missing: [],
        unused: [],
        coverage: 0
      },
      rtlUsage: {
        compliant: [],
        nonCompliant: [],
        coverage: 0
      },
      portals: {},
      summary: {
        totalFiles: 0,
        translatedFiles: 0,
        rtlCompliantFiles: 0,
        issues: []
      }
    };
  }

  /**
   * Main inspection function
   */
  async inspect() {
    console.log(`${colors.cyan}${colors.bright}🔍 Arabic Translation Inspector${colors.reset}`);
    console.log(`${colors.cyan}=====================================\n${colors.reset}`);

    try {
      // Step 1: Load and analyze I18n service
      console.log(`${colors.blue}📋 Step 1: Analyzing I18n Service...${colors.reset}`);
      await this.analyzeI18nService();

      // Step 2: Inspect all screen files
      console.log(`${colors.blue}📱 Step 2: Inspecting Screen Files...${colors.reset}`);
      await this.inspectScreenFiles();

      // Step 3: Inspect component files
      console.log(`${colors.blue}🧩 Step 3: Inspecting Component Files...${colors.reset}`);
      await this.inspectComponentFiles();

      // Step 4: Check RTL component usage
      console.log(`${colors.blue}🔄 Step 4: Checking RTL Component Usage...${colors.reset}`);
      await this.checkRTLUsage();

      // Step 5: Inspect Customer & Vendor Portals
      console.log(`${colors.blue}🏢 Step 5: Inspecting Customer & Vendor Portals...${colors.reset}`);
      await this.inspectPortals();

      // Step 6: Generate comprehensive report
      console.log(`${colors.blue}📊 Step 6: Generating Report...${colors.reset}`);
      this.generateReport();
      this.generatePortalReport();

    } catch (error) {
      console.error(`${colors.red}❌ Error during inspection: ${error.message}${colors.reset}`);
      process.exit(1);
    }
  }

  /**
   * Analyze I18n service for translation coverage
   */
  async analyzeI18nService() {
    if (!fs.existsSync(CONFIG.i18nServicePath)) {
      throw new Error('I18nService.ts not found');
    }

    const content = fs.readFileSync(CONFIG.i18nServicePath, 'utf8');
    
    // Extract English and Arabic translation keys
    const englishSection = this.extractTranslationSection(content, 'en:');
    const arabicSection = this.extractTranslationSection(content, 'ar:');
    
    const englishKeys = this.extractTranslationKeys(englishSection);
    const arabicKeys = this.extractTranslationKeys(arabicSection);
    
    // Find missing Arabic translations
    const missingArabic = englishKeys.filter(key => !arabicKeys.includes(key));
    const unusedArabic = arabicKeys.filter(key => !englishKeys.includes(key));
    
    this.results.translations = {
      missing: missingArabic,
      unused: unusedArabic,
      coverage: ((englishKeys.length - missingArabic.length) / englishKeys.length * 100).toFixed(2),
      totalKeys: englishKeys.length,
      translatedKeys: englishKeys.length - missingArabic.length
    };

    console.log(`   ✅ Found ${englishKeys.length} English translation keys`);
    console.log(`   ✅ Found ${arabicKeys.length} Arabic translation keys`);
    console.log(`   📊 Translation coverage: ${this.results.translations.coverage}%\n`);
  }

  /**
   * Extract translation section from I18n service
   */
  extractTranslationSection(content, sectionMarker) {
    const startIndex = content.indexOf(sectionMarker);
    if (startIndex === -1) return '';
    
    let braceCount = 0;
    let inSection = false;
    let section = '';
    
    for (let i = startIndex; i < content.length; i++) {
      const char = content[i];
      if (char === '{') {
        braceCount++;
        inSection = true;
      } else if (char === '}') {
        braceCount--;
      }
      
      if (inSection) {
        section += char;
      }
      
      if (inSection && braceCount === 0) {
        break;
      }
    }
    
    return section;
  }

  /**
   * Extract translation keys from a section
   */
  extractTranslationKeys(section) {
    const keys = [];
    const keyPattern = /(\w+):\s*['"`]/g;
    let match;
    
    while ((match = keyPattern.exec(section)) !== null) {
      keys.push(match[1]);
    }
    
    return keys;
  }

  /**
   * Inspect all screen files for translation usage
   */
  async inspectScreenFiles() {
    for (const screenDir of CONFIG.screenDirectories) {
      const fullPath = path.join(__dirname, '..', screenDir);
      if (fs.existsSync(fullPath)) {
        await this.inspectDirectory(fullPath, 'screen');
      }
    }
  }

  /**
   * Inspect component files for translation usage
   */
  async inspectComponentFiles() {
    for (const componentDir of CONFIG.componentDirectories) {
      const fullPath = path.join(__dirname, '..', componentDir);
      if (fs.existsSync(fullPath)) {
        await this.inspectDirectory(fullPath, 'component');
      }
    }
  }

  /**
   * Inspect a directory recursively
   */
  async inspectDirectory(dirPath, type) {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        await this.inspectDirectory(filePath, type);
      } else if (this.isReactFile(file)) {
        await this.inspectFile(filePath, type);
      }
    }
  }

  /**
   * Check if file is a React component file
   */
  isReactFile(filename) {
    return /\.(tsx?|jsx?)$/.test(filename) &&
           !CONFIG.excludePatterns.some(pattern => pattern.test(filename));
  }

  /**
   * Inspect individual file for translation and RTL usage
   */
  async inspectFile(filePath, type) {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(path.join(__dirname, '..'), filePath);
    
    const analysis = {
      path: relativePath,
      type: type,
      hasI18nImport: /import.*useI18n.*from/.test(content),
      hasRTLImport: TRANSLATION_PATTERNS.rtlImport.test(content),
      usesTranslation: TRANSLATION_PATTERNS.tFunction.test(content),
      hardcodedStrings: this.findHardcodedStrings(content),
      basicComponents: this.findBasicComponentUsage(content),
      rtlComponents: this.findRTLComponentUsage(content),
      translationKeys: this.extractUsedTranslationKeys(content)
    };
    
    // Determine compliance
    analysis.isTranslationCompliant = analysis.hasI18nImport && 
                                    analysis.usesTranslation && 
                                    analysis.hardcodedStrings.length === 0;
    
    analysis.isRTLCompliant = analysis.hasRTLImport && 
                             analysis.basicComponents.length === 0;
    
    this.results.summary.totalFiles++;
    if (analysis.isTranslationCompliant) this.results.summary.translatedFiles++;
    if (analysis.isRTLCompliant) this.results.summary.rtlCompliantFiles++;
    
    // Store results
    if (type === 'screen') {
      this.results.screens.push(analysis);
    } else {
      this.results.components.push(analysis);
    }
    
    // Track issues
    if (!analysis.isTranslationCompliant) {
      this.results.summary.issues.push({
        file: relativePath,
        type: 'translation',
        issues: [
          !analysis.hasI18nImport && 'Missing useI18n import',
          !analysis.usesTranslation && 'Not using translation function',
          analysis.hardcodedStrings.length > 0 && `${analysis.hardcodedStrings.length} hardcoded strings`
        ].filter(Boolean)
      });
    }
    
    if (!analysis.isRTLCompliant) {
      this.results.summary.issues.push({
        file: relativePath,
        type: 'rtl',
        issues: [
          !analysis.hasRTLImport && 'Missing RTL component import',
          analysis.basicComponents.length > 0 && `${analysis.basicComponents.length} basic components used`
        ].filter(Boolean)
      });
    }
  }

  /**
   * Find hardcoded strings in content
   */
  findHardcodedStrings(content) {
    const hardcoded = [];
    const matches = content.matchAll(TRANSLATION_PATTERNS.hardcodedText);

    for (const match of matches) {
      const text = match[1];
      // Skip common non-translatable strings
      if (!this.isTranslatableString(text)) continue;

      hardcoded.push({
        text: text,
        line: this.getLineNumber(content, match.index)
      });
    }

    return hardcoded;
  }

  /**
   * Check if string should be translated
   */
  isTranslatableString(text) {
    const nonTranslatablePatterns = [
      /^[0-9]+$/,           // Numbers only
      /^[A-Z_]+$/,          // Constants
      /^#[0-9A-Fa-f]+$/,    // Hex colors
      /^https?:\/\//,       // URLs
      /^[a-z-]+$/,          // CSS classes/IDs
      /^\w+\.\w+$/,         // File extensions
      /^(px|em|rem|%|vh|vw)$/, // CSS units
      /^(flex|center|left|right|top|bottom)$/, // Layout values
    ];

    return text.length > 2 &&
           !nonTranslatablePatterns.some(pattern => pattern.test(text)) &&
           /[A-Za-z]/.test(text);
  }

  /**
   * Find basic React Native component usage
   */
  findBasicComponentUsage(content) {
    const basicComponents = [];
    const matches = content.matchAll(TRANSLATION_PATTERNS.basicComponentUsage);

    for (const match of matches) {
      const component = match[1];
      if (RTL_COMPONENT_MAPPING[component]) {
        basicComponents.push({
          component: component,
          suggested: RTL_COMPONENT_MAPPING[component],
          line: this.getLineNumber(content, match.index)
        });
      }
    }

    return basicComponents;
  }

  /**
   * Find RTL component usage
   */
  findRTLComponentUsage(content) {
    const rtlComponents = [];
    const rtlPattern = /<(RTL\w+)\s/g;
    const matches = content.matchAll(rtlPattern);

    for (const match of matches) {
      rtlComponents.push({
        component: match[1],
        line: this.getLineNumber(content, match.index)
      });
    }

    return rtlComponents;
  }

  /**
   * Extract used translation keys from content
   */
  extractUsedTranslationKeys(content) {
    const keys = [];
    const matches = content.matchAll(TRANSLATION_PATTERNS.tFunction);

    for (const match of matches) {
      keys.push(match[1]);
    }

    return [...new Set(keys)]; // Remove duplicates
  }

  /**
   * Get line number for a character index
   */
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  /**
   * Check RTL component usage across codebase
   */
  async checkRTLUsage() {
    const compliantFiles = this.results.screens.concat(this.results.components)
      .filter(file => file.isRTLCompliant);

    const nonCompliantFiles = this.results.screens.concat(this.results.components)
      .filter(file => !file.isRTLCompliant);

    this.results.rtlUsage = {
      compliant: compliantFiles,
      nonCompliant: nonCompliantFiles,
      coverage: ((compliantFiles.length / (compliantFiles.length + nonCompliantFiles.length)) * 100).toFixed(2)
    };

    console.log(`   ✅ RTL compliant files: ${compliantFiles.length}`);
    console.log(`   ❌ Non-compliant files: ${nonCompliantFiles.length}`);
    console.log(`   📊 RTL coverage: ${this.results.rtlUsage.coverage}%\n`);
  }

  /**
   * Generate comprehensive report
   */
  generateReport() {
    console.log(`${colors.cyan}${colors.bright}📊 COMPREHENSIVE ARABIC TRANSLATION REPORT${colors.reset}`);
    console.log(`${colors.cyan}============================================\n${colors.reset}`);

    // Overall Summary
    this.printOverallSummary();

    // Translation Coverage
    this.printTranslationCoverage();

    // RTL Component Usage
    this.printRTLCoverage();

    // Screen Analysis
    this.printScreenAnalysis();

    // Component Analysis
    this.printComponentAnalysis();

    // Issues and Recommendations
    this.printIssuesAndRecommendations();

    // Final Assessment
    this.printFinalAssessment();
  }

  /**
   * Print overall summary
   */
  printOverallSummary() {
    console.log(`${colors.bright}📋 OVERALL SUMMARY${colors.reset}`);
    console.log(`${colors.cyan}==================${colors.reset}`);
    console.log(`Total Files Analyzed: ${colors.bright}${this.results.summary.totalFiles}${colors.reset}`);
    console.log(`Translation Compliant: ${colors.green}${this.results.summary.translatedFiles}${colors.reset} (${((this.results.summary.translatedFiles / this.results.summary.totalFiles) * 100).toFixed(1)}%)`);
    console.log(`RTL Compliant: ${colors.green}${this.results.summary.rtlCompliantFiles}${colors.reset} (${((this.results.summary.rtlCompliantFiles / this.results.summary.totalFiles) * 100).toFixed(1)}%)`);
    console.log(`Issues Found: ${colors.red}${this.results.summary.issues.length}${colors.reset}\n`);
  }

  /**
   * Print translation coverage details
   */
  printTranslationCoverage() {
    console.log(`${colors.bright}🌍 TRANSLATION COVERAGE${colors.reset}`);
    console.log(`${colors.cyan}=======================${colors.reset}`);
    console.log(`Total Translation Keys: ${colors.bright}${this.results.translations.totalKeys}${colors.reset}`);
    console.log(`Arabic Translations: ${colors.green}${this.results.translations.translatedKeys}${colors.reset}`);
    console.log(`Coverage: ${colors.bright}${this.results.translations.coverage}%${colors.reset}`);

    if (this.results.translations.missing.length > 0) {
      console.log(`${colors.red}Missing Arabic Translations (${this.results.translations.missing.length}):${colors.reset}`);
      this.results.translations.missing.slice(0, 10).forEach(key => {
        console.log(`  - ${key}`);
      });
      if (this.results.translations.missing.length > 10) {
        console.log(`  ... and ${this.results.translations.missing.length - 10} more`);
      }
    }
    console.log();
  }

  /**
   * Print RTL coverage details
   */
  printRTLCoverage() {
    console.log(`${colors.bright}🔄 RTL COMPONENT USAGE${colors.reset}`);
    console.log(`${colors.cyan}======================${colors.reset}`);
    console.log(`RTL Coverage: ${colors.bright}${this.results.rtlUsage.coverage}%${colors.reset}`);
    console.log(`Compliant Files: ${colors.green}${this.results.rtlUsage.compliant.length}${colors.reset}`);
    console.log(`Non-Compliant Files: ${colors.red}${this.results.rtlUsage.nonCompliant.length}${colors.reset}`);

    if (this.results.rtlUsage.nonCompliant.length > 0) {
      console.log(`${colors.red}Files needing RTL updates:${colors.reset}`);
      this.results.rtlUsage.nonCompliant.slice(0, 5).forEach(file => {
        console.log(`  - ${file.path} (${file.basicComponents.length} basic components)`);
      });
      if (this.results.rtlUsage.nonCompliant.length > 5) {
        console.log(`  ... and ${this.results.rtlUsage.nonCompliant.length - 5} more`);
      }
    }
    console.log();
  }

  /**
   * Print screen analysis
   */
  printScreenAnalysis() {
    console.log(`${colors.bright}📱 SCREEN ANALYSIS${colors.reset}`);
    console.log(`${colors.cyan}==================${colors.reset}`);

    const screensByCategory = this.groupScreensByCategory();

    Object.entries(screensByCategory).forEach(([category, screens]) => {
      const compliant = screens.filter(s => s.isTranslationCompliant && s.isRTLCompliant).length;
      const total = screens.length;
      const percentage = total > 0 ? ((compliant / total) * 100).toFixed(1) : '0';

      console.log(`${category}: ${colors.green}${compliant}${colors.reset}/${total} (${percentage}%) compliant`);
    });
    console.log();
  }

  /**
   * Group screens by category
   */
  groupScreensByCategory() {
    const categories = {};

    this.results.screens.forEach(screen => {
      const pathParts = screen.path.split('/');
      const category = pathParts[2] || 'other'; // src/screens/[category]

      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(screen);
    });

    return categories;
  }

  /**
   * Print component analysis
   */
  printComponentAnalysis() {
    console.log(`${colors.bright}🧩 COMPONENT ANALYSIS${colors.reset}`);
    console.log(`${colors.cyan}=====================${colors.reset}`);

    const compliantComponents = this.results.components.filter(c => c.isTranslationCompliant && c.isRTLCompliant).length;
    const totalComponents = this.results.components.length;
    const componentPercentage = totalComponents > 0 ? ((compliantComponents / totalComponents) * 100).toFixed(1) : '0';

    console.log(`Component Compliance: ${colors.green}${compliantComponents}${colors.reset}/${totalComponents} (${componentPercentage}%)`);
    console.log();
  }

  /**
   * Print issues and recommendations
   */
  printIssuesAndRecommendations() {
    console.log(`${colors.bright}⚠️  ISSUES & RECOMMENDATIONS${colors.reset}`);
    console.log(`${colors.cyan}=============================${colors.reset}`);

    if (this.results.summary.issues.length === 0) {
      console.log(`${colors.green}✅ No issues found! All files are properly translated and RTL-compliant.${colors.reset}\n`);
      return;
    }

    // Group issues by type
    const translationIssues = this.results.summary.issues.filter(i => i.type === 'translation');
    const rtlIssues = this.results.summary.issues.filter(i => i.type === 'rtl');

    if (translationIssues.length > 0) {
      console.log(`${colors.red}Translation Issues (${translationIssues.length}):${colors.reset}`);
      translationIssues.slice(0, 5).forEach(issue => {
        console.log(`  📄 ${issue.file}`);
        issue.issues.forEach(desc => console.log(`     - ${desc}`));
      });
      if (translationIssues.length > 5) {
        console.log(`     ... and ${translationIssues.length - 5} more files`);
      }
      console.log();
    }

    if (rtlIssues.length > 0) {
      console.log(`${colors.red}RTL Issues (${rtlIssues.length}):${colors.reset}`);
      rtlIssues.slice(0, 5).forEach(issue => {
        console.log(`  📄 ${issue.file}`);
        issue.issues.forEach(desc => console.log(`     - ${desc}`));
      });
      if (rtlIssues.length > 5) {
        console.log(`     ... and ${rtlIssues.length - 5} more files`);
      }
      console.log();
    }

    // Recommendations
    console.log(`${colors.yellow}📋 Recommendations:${colors.reset}`);
    if (translationIssues.length > 0) {
      console.log(`  1. Add useI18n import and t() function calls to files with hardcoded strings`);
      console.log(`  2. Replace hardcoded text with translation keys`);
    }
    if (rtlIssues.length > 0) {
      console.log(`  3. Import RTL components from '../components/RTL'`);
      console.log(`  4. Replace basic React Native components with RTL equivalents`);
    }
    console.log();
  }

  /**
   * Print final assessment
   */
  printFinalAssessment() {
    console.log(`${colors.bright}🎯 FINAL ASSESSMENT${colors.reset}`);
    console.log(`${colors.cyan}===================${colors.reset}`);

    const overallCompliance = ((this.results.summary.translatedFiles + this.results.summary.rtlCompliantFiles) / (this.results.summary.totalFiles * 2)) * 100;
    const translationCoverage = parseFloat(this.results.translations.coverage);
    const rtlCoverage = parseFloat(this.results.rtlUsage.coverage);

    let status, color;
    if (overallCompliance >= 95 && translationCoverage >= 95 && rtlCoverage >= 95) {
      status = 'EXCELLENT';
      color = colors.green;
    } else if (overallCompliance >= 80 && translationCoverage >= 80 && rtlCoverage >= 80) {
      status = 'GOOD';
      color = colors.yellow;
    } else {
      status = 'NEEDS IMPROVEMENT';
      color = colors.red;
    }

    console.log(`Overall Status: ${color}${status}${colors.reset}`);
    console.log(`Translation Coverage: ${translationCoverage >= 95 ? colors.green : colors.yellow}${translationCoverage}%${colors.reset}`);
    console.log(`RTL Coverage: ${rtlCoverage >= 95 ? colors.green : colors.yellow}${rtlCoverage}%${colors.reset}`);
    console.log(`Files Compliance: ${overallCompliance >= 95 ? colors.green : colors.yellow}${overallCompliance.toFixed(1)}%${colors.reset}`);

    if (status === 'EXCELLENT') {
      console.log(`\n${colors.green}🎉 Congratulations! Your app has excellent Arabic/RTL support!${colors.reset}`);
    } else {
      console.log(`\n${colors.yellow}💡 Focus on addressing the issues above to improve Arabic/RTL support.${colors.reset}`);
    }

    console.log(`\n${colors.cyan}📝 Report generated on: ${new Date().toLocaleString()}${colors.reset}`);
  }

  /**
   * Inspect specific portals (Customer and Vendor)
   */
  async inspectPortals() {
    console.log(`${colors.blue}🏢 Step 6: Inspecting Customer & Vendor Portals...${colors.reset}`);

    this.results.portals = {};

    for (const [portalKey, portalConfig] of Object.entries(CONFIG.portals)) {
      console.log(`\n${colors.magenta}   📱 Analyzing ${portalConfig.name}...${colors.reset}`);

      const portalResults = await this.inspectPortal(portalKey, portalConfig);
      this.results.portals[portalKey] = portalResults;

      // Print portal summary
      this.printPortalSummary(portalKey, portalResults);
    }
  }

  /**
   * Inspect individual portal
   */
  async inspectPortal(portalKey, portalConfig) {
    const portalResults = {
      name: portalConfig.name,
      priority: portalConfig.priority,
      screens: [],
      navigation: null,
      summary: {
        totalScreens: 0,
        translatedScreens: 0,
        rtlCompliantScreens: 0,
        issues: [],
        coverage: {
          translation: 0,
          rtl: 0,
          overall: 0
        }
      }
    };

    // Inspect navigation file
    if (portalConfig.navigation) {
      const navPath = path.join(__dirname, '..', portalConfig.navigation);
      if (fs.existsSync(navPath)) {
        portalResults.navigation = await this.inspectFile(navPath, 'navigation');
      } else {
        portalResults.summary.issues.push(`Navigation file missing: ${portalConfig.navigation}`);
      }
    }

    // Inspect all screens
    for (const screenPath of portalConfig.screens) {
      const fullPath = path.join(__dirname, '..', screenPath);
      if (fs.existsSync(fullPath)) {
        const screenResult = await this.inspectFile(fullPath, 'screen');
        portalResults.screens.push(screenResult);

        // Track compliance
        if (screenResult.usesTranslation && screenResult.hardcodedStrings.length === 0) {
          portalResults.summary.translatedScreens++;
        }
        if (screenResult.isRTLCompliant) {
          portalResults.summary.rtlCompliantScreens++;
        }
      } else {
        portalResults.summary.issues.push(`Screen file missing: ${screenPath}`);
      }
    }

    // Calculate coverage
    portalResults.summary.totalScreens = portalConfig.screens.length;
    portalResults.summary.coverage.translation =
      (portalResults.summary.translatedScreens / portalResults.summary.totalScreens * 100).toFixed(2);
    portalResults.summary.coverage.rtl =
      (portalResults.summary.rtlCompliantScreens / portalResults.summary.totalScreens * 100).toFixed(2);
    portalResults.summary.coverage.overall =
      ((portalResults.summary.translatedScreens + portalResults.summary.rtlCompliantScreens) /
       (portalResults.summary.totalScreens * 2) * 100).toFixed(2);

    return portalResults;
  }

  /**
   * Print portal summary
   */
  printPortalSummary(portalKey, portalResults) {
    const { summary } = portalResults;
    const translationColor = summary.coverage.translation >= 95 ? colors.green : colors.yellow;
    const rtlColor = summary.coverage.rtl >= 95 ? colors.green : colors.yellow;
    const overallColor = summary.coverage.overall >= 95 ? colors.green : colors.yellow;

    console.log(`     📊 ${portalResults.name} Summary:`);
    console.log(`        Screens: ${summary.totalScreens}`);
    console.log(`        Translation Coverage: ${translationColor}${summary.coverage.translation}%${colors.reset}`);
    console.log(`        RTL Coverage: ${rtlColor}${summary.coverage.rtl}%${colors.reset}`);
    console.log(`        Overall Coverage: ${overallColor}${summary.coverage.overall}%${colors.reset}`);

    if (summary.issues.length > 0) {
      console.log(`        ${colors.red}Issues: ${summary.issues.length}${colors.reset}`);
    }
  }

  /**
   * Generate portal-specific report
   */
  generatePortalReport() {
    if (!this.results.portals) return;

    console.log(`\n${colors.cyan}${colors.bright}🏢 PORTAL-SPECIFIC ANALYSIS${colors.reset}`);
    console.log(`${colors.cyan}============================\n${colors.reset}`);

    for (const [portalKey, portalResults] of Object.entries(this.results.portals)) {
      this.printDetailedPortalReport(portalKey, portalResults);
    }
  }

  /**
   * Print detailed portal report
   */
  printDetailedPortalReport(portalKey, portalResults) {
    const { summary } = portalResults;

    console.log(`${colors.magenta}${colors.bright}📱 ${portalResults.name} (Priority: ${portalResults.priority.toUpperCase()})${colors.reset}`);
    console.log(`${colors.magenta}${'='.repeat(50)}${colors.reset}`);

    // Overall metrics
    console.log(`\n📊 Overall Metrics:`);
    console.log(`   Total Screens: ${summary.totalScreens}`);
    console.log(`   Translated Screens: ${summary.translatedScreens}/${summary.totalScreens}`);
    console.log(`   RTL Compliant Screens: ${summary.rtlCompliantScreens}/${summary.totalScreens}`);

    const translationColor = summary.coverage.translation >= 95 ? colors.green : colors.yellow;
    const rtlColor = summary.coverage.rtl >= 95 ? colors.green : colors.yellow;
    const overallColor = summary.coverage.overall >= 95 ? colors.green : colors.yellow;

    console.log(`   Translation Coverage: ${translationColor}${summary.coverage.translation}%${colors.reset}`);
    console.log(`   RTL Coverage: ${rtlColor}${summary.coverage.rtl}%${colors.reset}`);
    console.log(`   Overall Coverage: ${overallColor}${summary.coverage.overall}%${colors.reset}`);

    // Navigation analysis
    if (portalResults.navigation) {
      console.log(`\n🧭 Navigation Analysis:`);
      const nav = portalResults.navigation;
      console.log(`   File: ${nav.path}`);
      console.log(`   Translation Usage: ${nav.usesTranslation ? colors.green + '✅' : colors.red + '❌'}${colors.reset}`);
      console.log(`   RTL Compliant: ${nav.isRTLCompliant ? colors.green + '✅' : colors.red + '❌'}${colors.reset}`);

      if (nav.hardcodedStrings.length > 0) {
        console.log(`   ${colors.yellow}⚠️  Hardcoded strings found: ${nav.hardcodedStrings.length}${colors.reset}`);
      }
    }

    // Screen-by-screen analysis
    console.log(`\n📱 Screen Analysis:`);
    portalResults.screens.forEach(screen => {
      const screenName = path.basename(screen.path, '.tsx');
      const translationStatus = screen.usesTranslation && screen.hardcodedStrings.length === 0 ?
        colors.green + '✅' : colors.red + '❌';
      const rtlStatus = screen.isRTLCompliant ? colors.green + '✅' : colors.red + '❌';

      console.log(`   ${screenName}:`);
      console.log(`     Translation: ${translationStatus}${colors.reset}`);
      console.log(`     RTL: ${rtlStatus}${colors.reset}`);

      if (screen.hardcodedStrings.length > 0) {
        console.log(`     ${colors.yellow}⚠️  Hardcoded strings: ${screen.hardcodedStrings.length}${colors.reset}`);
      }
      if (screen.basicComponents.length > 0) {
        console.log(`     ${colors.yellow}⚠️  Basic components: ${screen.basicComponents.length}${colors.reset}`);
      }
    });

    // Issues
    if (summary.issues.length > 0) {
      console.log(`\n${colors.red}❌ Issues Found:${colors.reset}`);
      summary.issues.forEach(issue => {
        console.log(`   • ${issue}`);
      });
    }

    // Recommendations
    this.printPortalRecommendations(portalKey, portalResults);

    console.log('\n');
  }

  /**
   * Print portal-specific recommendations
   */
  printPortalRecommendations(portalKey, portalResults) {
    const { summary } = portalResults;
    const recommendations = [];

    if (summary.coverage.translation < 95) {
      recommendations.push(`Improve translation coverage (currently ${summary.coverage.translation}%)`);
    }
    if (summary.coverage.rtl < 95) {
      recommendations.push(`Improve RTL component usage (currently ${summary.coverage.rtl}%)`);
    }
    if (summary.issues.length > 0) {
      recommendations.push(`Address ${summary.issues.length} missing files`);
    }

    // Screen-specific recommendations
    const screensWithIssues = portalResults.screens.filter(screen =>
      !screen.usesTranslation || screen.hardcodedStrings.length > 0 || !screen.isRTLCompliant
    );

    if (screensWithIssues.length > 0) {
      recommendations.push(`Fix ${screensWithIssues.length} screens with translation/RTL issues`);
    }

    if (recommendations.length > 0) {
      console.log(`\n${colors.blue}💡 Recommendations:${colors.reset}`);
      recommendations.forEach(rec => {
        console.log(`   • ${rec}`);
      });
    } else {
      console.log(`\n${colors.green}🎉 ${portalResults.name} is fully compliant!${colors.reset}`);
    }
  }
}

// Main execution
async function main() {
  try {
    const inspector = new ArabicTranslationInspector();
    await inspector.inspect();

    // Exit with appropriate code
    const hasIssues = inspector.results.summary.issues.length > 0;
    const translationCoverage = parseFloat(inspector.results.translations.coverage);
    const rtlCoverage = parseFloat(inspector.results.rtlUsage.coverage);

    // Check portal-specific coverage
    let portalIssues = false;
    if (inspector.results.portals) {
      for (const [portalKey, portalResults] of Object.entries(inspector.results.portals)) {
        const portalTranslation = parseFloat(portalResults.summary.coverage.translation);
        const portalRTL = parseFloat(portalResults.summary.coverage.rtl);
        const portalHasIssues = portalResults.summary.issues.length > 0;

        if (portalHasIssues || portalTranslation < 95 || portalRTL < 95) {
          portalIssues = true;
          break;
        }
      }
    }

    if (hasIssues || translationCoverage < 95 || rtlCoverage < 95 || portalIssues) {
      process.exit(1); // Exit with error code if issues found
    } else {
      process.exit(0); // Exit successfully
    }
  } catch (error) {
    console.error(`${colors.red}❌ Fatal error: ${error.message}${colors.reset}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { ArabicTranslationInspector, CONFIG };
