#!/usr/bin/env node

/**
 * Portal Summary Report for VendorHub
 * 
 * Generates a comprehensive summary of Customer and Vendor portal
 * translation and RTL compliance status with actionable recommendations.
 * 
 * Usage: node scripts/portal-summary-report.js
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Portal definitions
const PORTALS = {
  customer: {
    name: 'Customer Portal',
    screens: [
      'HomeScreen.tsx',
      'ShopsScreen.tsx', 
      'CartScreen.tsx',
      'ProductDetailsScreen.tsx',
      'CheckoutScreen.tsx',
      'VendorShopScreen.tsx',
      'SearchScreen.tsx',
      'ProfileScreen.tsx',
      'OrderHistoryScreen.tsx',
      'OrderDetailsScreen.tsx'
    ],
    directory: 'src/screens/public',
    navigation: 'src/navigation/CustomerNavigator.tsx',
    priority: 'CRITICAL'
  },
  vendor: {
    name: 'Vendor Portal',
    screens: [
      'VendorDashboardScreen.tsx',
      'VendorPendingScreen.tsx',
      'VendorProductsScreen.tsx',
      'VendorOrdersScreen.tsx',
      'AddProductScreen.tsx',
      'EditProductScreen.tsx',
      'ShopSettingsScreen.tsx'
    ],
    directory: 'src/screens/vendor',
    navigation: 'src/navigation/VendorNavigator.tsx',
    priority: 'CRITICAL'
  }
};

class PortalSummaryReporter {
  constructor() {
    this.baseDir = path.join(__dirname, '..');
    this.results = {
      customer: { screens: [], issues: [], coverage: 0 },
      vendor: { screens: [], issues: [], coverage: 0 },
      summary: { totalIssues: 0, criticalIssues: 0 }
    };
  }

  /**
   * Generate comprehensive portal summary
   */
  async generateSummary() {
    console.log(`${colors.cyan}${colors.bright}🏢 VendorHub Portal Summary Report${colors.reset}`);
    console.log(`${colors.cyan}===================================\n${colors.reset}`);

    // Analyze both portals
    await this.analyzePortal('customer');
    await this.analyzePortal('vendor');

    // Generate summary
    this.printExecutiveSummary();
    this.printDetailedFindings();
    this.printActionPlan();
    this.printNextSteps();
  }

  /**
   * Analyze individual portal
   */
  async analyzePortal(portalKey) {
    const portal = PORTALS[portalKey];
    const portalResult = this.results[portalKey];

    console.log(`${colors.blue}📱 Analyzing ${portal.name}...${colors.reset}`);

    // Check navigation file
    const navPath = path.join(this.baseDir, portal.navigation);
    if (fs.existsSync(navPath)) {
      const navAnalysis = this.quickAnalyzeFile(navPath);
      if (!navAnalysis.hasTranslation) {
        portalResult.issues.push(`Navigation: Missing translation support`);
      }
      if (!navAnalysis.hasRTL) {
        portalResult.issues.push(`Navigation: Not RTL compliant`);
      }
    } else {
      portalResult.issues.push(`Navigation: File missing (${portal.navigation})`);
    }

    // Check each screen
    let compliantScreens = 0;
    for (const screenFile of portal.screens) {
      const screenPath = path.join(this.baseDir, portal.directory, screenFile);
      const screenName = screenFile.replace('.tsx', '');
      
      if (fs.existsSync(screenPath)) {
        const analysis = this.quickAnalyzeFile(screenPath);
        const screenResult = {
          name: screenName,
          path: screenPath,
          hasTranslation: analysis.hasTranslation,
          hasRTL: analysis.hasRTL,
          hardcodedCount: analysis.hardcodedCount,
          basicComponentCount: analysis.basicComponentCount,
          isCompliant: analysis.hasTranslation && analysis.hasRTL && analysis.hardcodedCount === 0
        };

        portalResult.screens.push(screenResult);

        if (screenResult.isCompliant) {
          compliantScreens++;
        } else {
          if (!analysis.hasTranslation) {
            portalResult.issues.push(`${screenName}: Missing translation support`);
          }
          if (!analysis.hasRTL) {
            portalResult.issues.push(`${screenName}: Not RTL compliant`);
          }
          if (analysis.hardcodedCount > 0) {
            portalResult.issues.push(`${screenName}: ${analysis.hardcodedCount} hardcoded strings`);
          }
        }
      } else {
        portalResult.issues.push(`${screenName}: File missing`);
      }
    }

    portalResult.coverage = (compliantScreens / portal.screens.length * 100).toFixed(1);
    console.log(`   Coverage: ${portalResult.coverage}% (${compliantScreens}/${portal.screens.length} screens)\n`);
  }

  /**
   * Quick file analysis
   */
  quickAnalyzeFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    return {
      hasTranslation: /useI18n\(\)/.test(content) && /\bt\(['"`]/.test(content),
      hasRTL: /import.*RTL.*from.*RTL/.test(content),
      hardcodedCount: this.countHardcodedStrings(content),
      basicComponentCount: this.countBasicComponents(content)
    };
  }

  /**
   * Count hardcoded strings
   */
  countHardcodedStrings(content) {
    const matches = content.match(/['"`]([A-Za-z][^'"`]*[A-Za-z])['"]/g) || [];
    return matches.filter(match => {
      const text = match.slice(1, -1);
      return text.length > 2 && 
             !/^[0-9]+$/.test(text) && 
             !/^[A-Z_]+$/.test(text) &&
             !/^#[0-9A-Fa-f]+$/.test(text) &&
             !/^https?:\/\//.test(text);
    }).length;
  }

  /**
   * Count basic React Native components
   */
  countBasicComponents(content) {
    const matches = content.match(/<(View|Text|SafeAreaView|ScrollView|FlatList|SectionList|TextInput)\s/g) || [];
    return matches.length;
  }

  /**
   * Print executive summary
   */
  printExecutiveSummary() {
    const customerCoverage = parseFloat(this.results.customer.coverage);
    const vendorCoverage = parseFloat(this.results.vendor.coverage);
    const totalIssues = this.results.customer.issues.length + this.results.vendor.issues.length;

    console.log(`${colors.cyan}${colors.bright}📊 EXECUTIVE SUMMARY${colors.reset}`);
    console.log(`${colors.cyan}===================\n${colors.reset}`);

    // Overall status
    const overallStatus = (customerCoverage >= 95 && vendorCoverage >= 95) ? 
      `${colors.green}✅ COMPLIANT` : `${colors.red}❌ NON-COMPLIANT`;
    
    console.log(`Overall Status: ${overallStatus}${colors.reset}`);
    console.log(`Total Issues Found: ${totalIssues}`);
    console.log(`Customer Portal: ${this.getCoverageColor(customerCoverage)}${customerCoverage}%${colors.reset}`);
    console.log(`Vendor Portal: ${this.getCoverageColor(vendorCoverage)}${vendorCoverage}%${colors.reset}\n`);

    // Priority assessment
    if (totalIssues === 0) {
      console.log(`${colors.green}🎉 Both portals are fully compliant with Arabic/RTL requirements!${colors.reset}\n`);
    } else if (totalIssues < 10) {
      console.log(`${colors.yellow}⚠️  Minor issues found. Quick fixes needed.${colors.reset}\n`);
    } else {
      console.log(`${colors.red}🚨 Significant work required for full compliance.${colors.reset}\n`);
    }
  }

  /**
   * Get color for coverage percentage
   */
  getCoverageColor(coverage) {
    if (coverage >= 95) return colors.green;
    if (coverage >= 75) return colors.yellow;
    return colors.red;
  }

  /**
   * Print detailed findings
   */
  printDetailedFindings() {
    console.log(`${colors.cyan}${colors.bright}🔍 DETAILED FINDINGS${colors.reset}`);
    console.log(`${colors.cyan}===================\n${colors.reset}`);

    // Customer Portal
    this.printPortalFindings('Customer Portal', this.results.customer);
    
    // Vendor Portal  
    this.printPortalFindings('Vendor Portal', this.results.vendor);
  }

  /**
   * Print findings for specific portal
   */
  printPortalFindings(title, portalResult) {
    console.log(`${colors.magenta}${colors.bright}📱 ${title}${colors.reset}`);
    console.log(`Coverage: ${this.getCoverageColor(portalResult.coverage)}${portalResult.coverage}%${colors.reset}`);
    console.log(`Issues: ${portalResult.issues.length}\n`);

    // Screen status
    console.log(`Screen Status:`);
    portalResult.screens.forEach(screen => {
      const status = screen.isCompliant ? 
        `${colors.green}✅` : `${colors.red}❌`;
      console.log(`   ${status} ${screen.name}${colors.reset}`);
    });

    // Top issues
    if (portalResult.issues.length > 0) {
      console.log(`\nTop Issues:`);
      portalResult.issues.slice(0, 5).forEach(issue => {
        console.log(`   • ${issue}`);
      });
      if (portalResult.issues.length > 5) {
        console.log(`   ... and ${portalResult.issues.length - 5} more`);
      }
    }
    console.log('');
  }

  /**
   * Print action plan
   */
  printActionPlan() {
    console.log(`${colors.cyan}${colors.bright}📋 ACTION PLAN${colors.reset}`);
    console.log(`${colors.cyan}==============\n${colors.reset}`);

    const customerCoverage = parseFloat(this.results.customer.coverage);
    const vendorCoverage = parseFloat(this.results.vendor.coverage);

    // Priority order
    const priorities = [];
    if (customerCoverage < vendorCoverage) {
      priorities.push('customer', 'vendor');
    } else {
      priorities.push('vendor', 'customer');
    }

    console.log(`${colors.blue}Recommended Priority Order:${colors.reset}`);
    priorities.forEach((portal, index) => {
      const portalName = PORTALS[portal].name;
      const coverage = this.results[portal].coverage;
      console.log(`   ${index + 1}. ${portalName} (${coverage}% coverage)`);
    });

    console.log(`\n${colors.blue}Immediate Actions:${colors.reset}`);
    console.log(`   1. Fix navigation files for both portals`);
    console.log(`   2. Add translation support to non-compliant screens`);
    console.log(`   3. Replace basic components with RTL components`);
    console.log(`   4. Replace hardcoded strings with translation keys`);
    console.log(`   5. Test RTL layout and functionality\n`);
  }

  /**
   * Print next steps
   */
  printNextSteps() {
    console.log(`${colors.cyan}${colors.bright}🚀 NEXT STEPS${colors.reset}`);
    console.log(`${colors.cyan}=============\n${colors.reset}`);

    console.log(`${colors.green}1. Run detailed inspection:${colors.reset}`);
    console.log(`   node scripts/portal-translation-inspector.js\n`);

    console.log(`${colors.green}2. Fix high-priority issues first:${colors.reset}`);
    console.log(`   • Navigation files`);
    console.log(`   • Screens missing translation support\n`);

    console.log(`${colors.green}3. Systematic screen updates:${colors.reset}`);
    console.log(`   • Import useI18n hook`);
    console.log(`   • Replace hardcoded strings with t() calls`);
    console.log(`   • Import and use RTL components\n`);

    console.log(`${colors.green}4. Validation:${colors.reset}`);
    console.log(`   • Re-run inspection scripts`);
    console.log(`   • Test in Arabic language mode`);
    console.log(`   • Verify RTL layout behavior\n`);

    const totalIssues = this.results.customer.issues.length + this.results.vendor.issues.length;
    const estimatedHours = Math.ceil(totalIssues * 0.5); // 30 minutes per issue

    console.log(`${colors.blue}📅 Estimated effort: ${estimatedHours} hours${colors.reset}`);
    console.log(`${colors.cyan}📝 Report generated: ${new Date().toLocaleString()}${colors.reset}`);
  }
}

// Main execution
async function main() {
  try {
    const reporter = new PortalSummaryReporter();
    await reporter.generateSummary();
  } catch (error) {
    console.error(`${colors.red}❌ Error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { PortalSummaryReporter };
