#!/usr/bin/env node

/**
 * Portal Translation Inspector for VendorHub
 * 
 * Focused inspection script for Customer and Vendor portals
 * to ensure complete Arabic translation coverage and RTL compliance.
 * 
 * Usage: node scripts/portal-translation-inspector.js
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Portal Configuration
const PORTAL_CONFIG = {
  customer: {
    name: 'Customer Portal',
    description: 'Public-facing customer interface',
    priority: 'CRITICAL',
    screens: [
      'src/screens/public/HomeScreen.tsx',
      'src/screens/public/ShopsScreen.tsx',
      'src/screens/public/CartScreen.tsx',
      'src/screens/public/ProductDetailsScreen.tsx',
      'src/screens/public/CheckoutScreen.tsx',
      'src/screens/public/VendorShopScreen.tsx',
      'src/screens/public/SearchScreen.tsx',
      'src/screens/public/ProfileScreen.tsx',
      'src/screens/public/OrderHistoryScreen.tsx',
      'src/screens/public/OrderDetailsScreen.tsx'
    ],
    navigation: 'src/navigation/CustomerNavigator.tsx',
    components: [
      'src/components/customer',
      'src/components/public'
    ]
  },
  vendor: {
    name: 'Vendor Portal',
    description: 'Vendor management interface',
    priority: 'CRITICAL',
    screens: [
      'src/screens/vendor/VendorDashboardScreen.tsx',
      'src/screens/vendor/VendorPendingScreen.tsx',
      'src/screens/vendor/VendorProductsScreen.tsx',
      'src/screens/vendor/VendorOrdersScreen.tsx',
      'src/screens/vendor/AddProductScreen.tsx',
      'src/screens/vendor/EditProductScreen.tsx',
      'src/screens/vendor/ShopSettingsScreen.tsx'
    ],
    navigation: 'src/navigation/VendorNavigator.tsx',
    components: [
      'src/components/vendor'
    ]
  }
};

// Translation and RTL patterns
const PATTERNS = {
  useI18n: /useI18n\(\)/g,
  tFunction: /\bt\(['"`]([^'"`]+)['"`]\)/g,
  hardcodedText: /['"`]([A-Za-z][^'"`]*[A-Za-z])['"]/g,
  rtlImport: /import.*\{.*RTL.*\}.*from.*RTL/g,
  basicComponents: /<(View|Text|SafeAreaView|ScrollView|FlatList|SectionList|TextInput|Ionicons)\s/g,
  rtlComponents: /<(RTL\w+)\s/g
};

class PortalTranslationInspector {
  constructor() {
    this.results = {
      customer: null,
      vendor: null,
      summary: {
        totalPortals: 2,
        compliantPortals: 0,
        totalScreens: 0,
        compliantScreens: 0,
        overallCoverage: 0,
        criticalIssues: []
      }
    };
  }

  /**
   * Main inspection function
   */
  async inspect() {
    console.log(`${colors.cyan}${colors.bright}🏢 Portal Translation Inspector${colors.reset}`);
    console.log(`${colors.cyan}=====================================\n${colors.reset}`);

    try {
      // Inspect Customer Portal
      console.log(`${colors.blue}📱 Step 1: Inspecting Customer Portal...${colors.reset}`);
      this.results.customer = await this.inspectPortal('customer', PORTAL_CONFIG.customer);

      // Inspect Vendor Portal
      console.log(`${colors.blue}🏪 Step 2: Inspecting Vendor Portal...${colors.reset}`);
      this.results.vendor = await this.inspectPortal('vendor', PORTAL_CONFIG.vendor);

      // Generate comprehensive report
      console.log(`${colors.blue}📊 Step 3: Generating Portal Report...${colors.reset}`);
      this.generatePortalReport();

      // Calculate overall summary
      this.calculateOverallSummary();

    } catch (error) {
      console.error(`${colors.red}❌ Error during inspection: ${error.message}${colors.reset}`);
      process.exit(1);
    }
  }

  /**
   * Inspect individual portal
   */
  async inspectPortal(portalKey, config) {
    const portalResult = {
      name: config.name,
      description: config.description,
      priority: config.priority,
      screens: [],
      navigation: null,
      components: [],
      metrics: {
        totalScreens: config.screens.length,
        translatedScreens: 0,
        rtlCompliantScreens: 0,
        fullyCompliantScreens: 0,
        translationCoverage: 0,
        rtlCoverage: 0,
        overallCoverage: 0
      },
      issues: {
        critical: [],
        warning: [],
        info: []
      }
    };

    // Inspect navigation
    if (config.navigation) {
      const navPath = path.join(__dirname, '..', config.navigation);
      if (fs.existsSync(navPath)) {
        portalResult.navigation = await this.inspectFile(navPath, 'navigation');
      } else {
        portalResult.issues.critical.push(`Navigation file missing: ${config.navigation}`);
      }
    }

    // Inspect screens
    for (const screenPath of config.screens) {
      const fullPath = path.join(__dirname, '..', screenPath);
      if (fs.existsSync(fullPath)) {
        const screenResult = await this.inspectFile(fullPath, 'screen');
        portalResult.screens.push(screenResult);
        
        // Update metrics
        if (screenResult.usesTranslation && screenResult.hardcodedStrings.length === 0) {
          portalResult.metrics.translatedScreens++;
        }
        if (screenResult.isRTLCompliant) {
          portalResult.metrics.rtlCompliantScreens++;
        }
        if (screenResult.isFullyCompliant) {
          portalResult.metrics.fullyCompliantScreens++;
        }
      } else {
        portalResult.issues.critical.push(`Screen file missing: ${screenPath}`);
      }
    }

    // Calculate coverage percentages
    const { metrics } = portalResult;
    metrics.translationCoverage = (metrics.translatedScreens / metrics.totalScreens * 100).toFixed(2);
    metrics.rtlCoverage = (metrics.rtlCompliantScreens / metrics.totalScreens * 100).toFixed(2);
    metrics.overallCoverage = (metrics.fullyCompliantScreens / metrics.totalScreens * 100).toFixed(2);

    // Categorize issues
    this.categorizePortalIssues(portalResult);

    console.log(`   ✅ ${config.name} analysis complete`);
    console.log(`      Translation Coverage: ${metrics.translationCoverage}%`);
    console.log(`      RTL Coverage: ${metrics.rtlCoverage}%`);
    console.log(`      Overall Coverage: ${metrics.overallCoverage}%\n`);

    return portalResult;
  }

  /**
   * Inspect individual file
   */
  async inspectFile(filePath, type) {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(path.join(__dirname, '..'), filePath);
    
    const analysis = {
      path: relativePath,
      type: type,
      hasI18nImport: /import.*useI18n.*from/.test(content),
      hasRTLImport: PATTERNS.rtlImport.test(content),
      usesTranslation: PATTERNS.tFunction.test(content),
      hardcodedStrings: this.findHardcodedStrings(content),
      basicComponents: this.findBasicComponents(content),
      rtlComponents: this.findRTLComponents(content),
      translationKeys: this.extractTranslationKeys(content),
      isRTLCompliant: false,
      isFullyCompliant: false
    };

    // Determine RTL compliance
    analysis.isRTLCompliant = analysis.hasRTLImport && analysis.basicComponents.length === 0;
    
    // Determine full compliance
    analysis.isFullyCompliant = analysis.usesTranslation && 
                               analysis.hardcodedStrings.length === 0 && 
                               analysis.isRTLCompliant;

    return analysis;
  }

  /**
   * Find hardcoded strings in content
   */
  findHardcodedStrings(content) {
    const hardcoded = [];
    const matches = content.matchAll(PATTERNS.hardcodedText);

    for (const match of matches) {
      const text = match[1];
      // Filter out non-translatable strings
      if (this.isTranslatableString(text)) {
        hardcoded.push({
          text: text,
          line: this.getLineNumber(content, match.index)
        });
      }
    }

    return hardcoded;
  }

  /**
   * Check if string should be translated
   */
  isTranslatableString(text) {
    const nonTranslatablePatterns = [
      /^[0-9]+$/,           // Numbers only
      /^[A-Z_]+$/,          // Constants
      /^[a-z-]+$/,          // CSS classes/IDs
      /^\w+\.\w+$/,         // Object properties
      /^#[0-9A-Fa-f]+$/,    // Hex colors
      /^https?:\/\//,       // URLs
      /^[A-Z]{2,}$/,        // Abbreviations
      /^\w+Icon$/,          // Icon names
      /^(flex|center|row|column|absolute|relative)$/ // Style values
    ];

    return !nonTranslatablePatterns.some(pattern => pattern.test(text)) &&
           text.length > 2 &&
           /[A-Za-z]/.test(text);
  }

  /**
   * Find basic React Native components
   */
  findBasicComponents(content) {
    const basic = [];
    const matches = content.matchAll(PATTERNS.basicComponents);

    for (const match of matches) {
      basic.push({
        component: match[1],
        line: this.getLineNumber(content, match.index)
      });
    }

    return basic;
  }

  /**
   * Find RTL components
   */
  findRTLComponents(content) {
    const rtl = [];
    const matches = content.matchAll(PATTERNS.rtlComponents);

    for (const match of matches) {
      rtl.push({
        component: match[1],
        line: this.getLineNumber(content, match.index)
      });
    }

    return rtl;
  }

  /**
   * Extract translation keys
   */
  extractTranslationKeys(content) {
    const keys = [];
    const matches = content.matchAll(PATTERNS.tFunction);

    for (const match of matches) {
      keys.push(match[1]);
    }

    return [...new Set(keys)];
  }

  /**
   * Get line number for character index
   */
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  /**
   * Categorize portal issues by severity
   */
  categorizePortalIssues(portalResult) {
    const { screens, navigation, metrics } = portalResult;

    // Check navigation issues
    if (navigation && !navigation.isFullyCompliant) {
      if (!navigation.usesTranslation) {
        portalResult.issues.critical.push(`Navigation missing translation support`);
      }
      if (!navigation.isRTLCompliant) {
        portalResult.issues.critical.push(`Navigation not RTL compliant`);
      }
    }

    // Check screen issues
    screens.forEach(screen => {
      const screenName = path.basename(screen.path, '.tsx');

      if (!screen.usesTranslation) {
        portalResult.issues.critical.push(`${screenName}: Missing translation support`);
      }
      if (screen.hardcodedStrings.length > 0) {
        portalResult.issues.warning.push(`${screenName}: ${screen.hardcodedStrings.length} hardcoded strings`);
      }
      if (!screen.isRTLCompliant) {
        portalResult.issues.warning.push(`${screenName}: Not RTL compliant`);
      }
      if (screen.basicComponents.length > 0) {
        portalResult.issues.info.push(`${screenName}: ${screen.basicComponents.length} basic components`);
      }
    });

    // Coverage-based issues
    if (metrics.translationCoverage < 95) {
      portalResult.issues.critical.push(`Translation coverage below 95% (${metrics.translationCoverage}%)`);
    }
    if (metrics.rtlCoverage < 95) {
      portalResult.issues.warning.push(`RTL coverage below 95% (${metrics.rtlCoverage}%)`);
    }
  }

  /**
   * Generate comprehensive portal report
   */
  generatePortalReport() {
    console.log(`\n${colors.cyan}${colors.bright}📊 PORTAL TRANSLATION REPORT${colors.reset}`);
    console.log(`${colors.cyan}==============================\n${colors.reset}`);

    // Customer Portal Report
    this.printPortalDetails('Customer Portal', this.results.customer);

    // Vendor Portal Report
    this.printPortalDetails('Vendor Portal', this.results.vendor);

    // Comparison and recommendations
    this.printPortalComparison();
  }

  /**
   * Print detailed portal information
   */
  printPortalDetails(title, portal) {
    console.log(`${colors.magenta}${colors.bright}🏢 ${title}${colors.reset}`);
    console.log(`${colors.magenta}${'='.repeat(50)}${colors.reset}`);
    console.log(`Priority: ${portal.priority}`);
    console.log(`Description: ${portal.description}\n`);

    // Metrics
    const { metrics } = portal;
    console.log(`📊 Coverage Metrics:`);
    console.log(`   Total Screens: ${metrics.totalScreens}`);
    console.log(`   Fully Compliant: ${metrics.fullyCompliantScreens}/${metrics.totalScreens}`);

    const translationColor = metrics.translationCoverage >= 95 ? colors.green : colors.yellow;
    const rtlColor = metrics.rtlCoverage >= 95 ? colors.green : colors.yellow;
    const overallColor = metrics.overallCoverage >= 95 ? colors.green : colors.yellow;

    console.log(`   Translation Coverage: ${translationColor}${metrics.translationCoverage}%${colors.reset}`);
    console.log(`   RTL Coverage: ${rtlColor}${metrics.rtlCoverage}%${colors.reset}`);
    console.log(`   Overall Coverage: ${overallColor}${metrics.overallCoverage}%${colors.reset}`);

    // Navigation status
    if (portal.navigation) {
      console.log(`\n🧭 Navigation Status:`);
      const navStatus = portal.navigation.isFullyCompliant ?
        colors.green + '✅ Fully Compliant' : colors.red + '❌ Issues Found';
      console.log(`   ${navStatus}${colors.reset}`);
    }

    // Screen breakdown
    console.log(`\n📱 Screen Breakdown:`);
    portal.screens.forEach(screen => {
      const screenName = path.basename(screen.path, '.tsx');
      const status = screen.isFullyCompliant ?
        colors.green + '✅' : colors.red + '❌';
      console.log(`   ${status} ${screenName}${colors.reset}`);

      if (!screen.isFullyCompliant) {
        if (!screen.usesTranslation) console.log(`      • Missing translation support`);
        if (screen.hardcodedStrings.length > 0) console.log(`      • ${screen.hardcodedStrings.length} hardcoded strings`);
        if (!screen.isRTLCompliant) console.log(`      • Not RTL compliant`);
      }
    });

    // Issues summary
    const { issues } = portal;
    if (issues.critical.length > 0) {
      console.log(`\n${colors.red}❌ Critical Issues (${issues.critical.length}):${colors.reset}`);
      issues.critical.forEach(issue => console.log(`   • ${issue}`));
    }
    if (issues.warning.length > 0) {
      console.log(`\n${colors.yellow}⚠️  Warnings (${issues.warning.length}):${colors.reset}`);
      issues.warning.forEach(issue => console.log(`   • ${issue}`));
    }
    if (issues.info.length > 0) {
      console.log(`\n${colors.blue}ℹ️  Info (${issues.info.length}):${colors.reset}`);
      issues.info.forEach(issue => console.log(`   • ${issue}`));
    }

    console.log('\n');
  }

  /**
   * Print portal comparison and recommendations
   */
  printPortalComparison() {
    console.log(`${colors.cyan}${colors.bright}🔄 PORTAL COMPARISON${colors.reset}`);
    console.log(`${colors.cyan}====================\n${colors.reset}`);

    const customer = this.results.customer;
    const vendor = this.results.vendor;

    console.log(`📊 Coverage Comparison:`);
    console.log(`   Customer Portal: ${customer.metrics.overallCoverage}%`);
    console.log(`   Vendor Portal: ${vendor.metrics.overallCoverage}%`);

    const betterPortal = parseFloat(customer.metrics.overallCoverage) > parseFloat(vendor.metrics.overallCoverage) ?
      'Customer' : 'Vendor';
    console.log(`   Better Performing: ${betterPortal} Portal\n`);

    // Priority recommendations
    console.log(`${colors.blue}💡 Priority Recommendations:${colors.reset}`);

    const allCriticalIssues = [
      ...customer.issues.critical.map(issue => `Customer: ${issue}`),
      ...vendor.issues.critical.map(issue => `Vendor: ${issue}`)
    ];

    if (allCriticalIssues.length > 0) {
      console.log(`   🚨 Address ${allCriticalIssues.length} critical issues first:`);
      allCriticalIssues.forEach(issue => console.log(`      • ${issue}`));
    } else {
      console.log(`   🎉 No critical issues found!`);
    }

    // Next steps
    console.log(`\n${colors.green}📋 Next Steps:${colors.reset}`);
    if (parseFloat(customer.metrics.overallCoverage) < 95) {
      console.log(`   1. Focus on Customer Portal (${customer.metrics.overallCoverage}% coverage)`);
    }
    if (parseFloat(vendor.metrics.overallCoverage) < 95) {
      console.log(`   2. Improve Vendor Portal (${vendor.metrics.overallCoverage}% coverage)`);
    }
    if (parseFloat(customer.metrics.overallCoverage) >= 95 && parseFloat(vendor.metrics.overallCoverage) >= 95) {
      console.log(`   ✅ Both portals are fully compliant! Consider maintenance and monitoring.`);
    }

    console.log(`\n${colors.cyan}📝 Report generated on: ${new Date().toLocaleString()}${colors.reset}`);
  }

  /**
   * Calculate overall summary
   */
  calculateOverallSummary() {
    const customer = this.results.customer;
    const vendor = this.results.vendor;

    this.results.summary.totalScreens = customer.metrics.totalScreens + vendor.metrics.totalScreens;
    this.results.summary.compliantScreens = customer.metrics.fullyCompliantScreens + vendor.metrics.fullyCompliantScreens;

    const customerCompliant = parseFloat(customer.metrics.overallCoverage) >= 95;
    const vendorCompliant = parseFloat(vendor.metrics.overallCoverage) >= 95;

    this.results.summary.compliantPortals = (customerCompliant ? 1 : 0) + (vendorCompliant ? 1 : 0);
    this.results.summary.overallCoverage = (this.results.summary.compliantScreens / this.results.summary.totalScreens * 100).toFixed(2);

    // Collect all critical issues
    this.results.summary.criticalIssues = [
      ...customer.issues.critical,
      ...vendor.issues.critical
    ];
  }
}

// Main execution
async function main() {
  try {
    const inspector = new PortalTranslationInspector();
    await inspector.inspect();

    // Exit with appropriate code based on compliance
    const customerCoverage = parseFloat(inspector.results.customer.metrics.overallCoverage);
    const vendorCoverage = parseFloat(inspector.results.vendor.metrics.overallCoverage);
    const criticalIssues = inspector.results.summary.criticalIssues.length;

    if (customerCoverage < 95 || vendorCoverage < 95 || criticalIssues > 0) {
      process.exit(1);
    } else {
      process.exit(0);
    }
  } catch (error) {
    console.error(`${colors.red}❌ Fatal error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { PortalTranslationInspector, PORTAL_CONFIG };
