#!/usr/bin/env node

/**
 * Portal Validation Checklist for VendorHub
 * 
 * Generates a detailed checklist for each screen in Customer and Vendor portals
 * with specific actions needed for Arabic/RTL compliance.
 * 
 * Usage: node scripts/portal-validation-checklist.js
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Portal configuration
const PORTALS = {
  customer: {
    name: 'Customer Portal',
    screens: [
      'HomeScreen.tsx',
      'ShopsScreen.tsx',
      'CartScreen.tsx', 
      'ProductDetailsScreen.tsx',
      'CheckoutScreen.tsx',
      'VendorShopScreen.tsx',
      'SearchScreen.tsx',
      'ProfileScreen.tsx',
      'OrderHistoryScreen.tsx',
      'OrderDetailsScreen.tsx'
    ],
    directory: 'src/screens/public',
    navigation: 'src/navigation/CustomerNavigator.tsx'
  },
  vendor: {
    name: 'Vendor Portal',
    screens: [
      'VendorDashboardScreen.tsx',
      'VendorPendingScreen.tsx',
      'VendorProductsScreen.tsx',
      'VendorOrdersScreen.tsx',
      'AddProductScreen.tsx',
      'EditProductScreen.tsx',
      'ShopSettingsScreen.tsx'
    ],
    directory: 'src/screens/vendor',
    navigation: 'src/navigation/VendorNavigator.tsx'
  }
};

class PortalValidationChecker {
  constructor() {
    this.baseDir = path.join(__dirname, '..');
    this.checklist = {
      customer: [],
      vendor: [],
      summary: { totalTasks: 0, completedTasks: 0 }
    };
  }

  /**
   * Generate validation checklist
   */
  async generateChecklist() {
    console.log(`${colors.cyan}${colors.bright}✅ Portal Validation Checklist${colors.reset}`);
    console.log(`${colors.cyan}===============================\n${colors.reset}`);

    // Generate checklists for both portals
    await this.generatePortalChecklist('customer');
    await this.generatePortalChecklist('vendor');

    // Print comprehensive checklist
    this.printChecklist();
    this.printSummary();
  }

  /**
   * Generate checklist for specific portal
   */
  async generatePortalChecklist(portalKey) {
    const portal = PORTALS[portalKey];
    const portalChecklist = [];

    console.log(`${colors.blue}📋 Generating ${portal.name} checklist...${colors.reset}`);

    // Navigation checklist
    const navPath = path.join(this.baseDir, portal.navigation);
    if (fs.existsSync(navPath)) {
      const navTasks = this.generateFileChecklist(navPath, 'Navigation');
      portalChecklist.push(...navTasks);
    }

    // Screen checklists
    for (const screenFile of portal.screens) {
      const screenPath = path.join(this.baseDir, portal.directory, screenFile);
      const screenName = screenFile.replace('.tsx', '');
      
      if (fs.existsSync(screenPath)) {
        const screenTasks = this.generateFileChecklist(screenPath, screenName);
        portalChecklist.push(...screenTasks);
      } else {
        portalChecklist.push({
          file: screenName,
          type: 'CRITICAL',
          task: 'Create missing screen file',
          status: '❌',
          completed: false
        });
      }
    }

    this.checklist[portalKey] = portalChecklist;
    console.log(`   Generated ${portalChecklist.length} tasks\n`);
  }

  /**
   * Generate checklist for individual file
   */
  generateFileChecklist(filePath, fileName) {
    const content = fs.readFileSync(filePath, 'utf8');
    const tasks = [];

    // Check translation support
    const hasI18nImport = /import.*useI18n.*from/.test(content);
    const hasI18nUsage = /useI18n\(\)/.test(content);
    const hasTranslationCalls = /\bt\(['"`]/.test(content);

    if (!hasI18nImport) {
      tasks.push({
        file: fileName,
        type: 'CRITICAL',
        task: 'Add useI18n import: import { useI18n } from \'../../hooks\';',
        status: '❌',
        completed: false
      });
    }

    if (!hasI18nUsage) {
      tasks.push({
        file: fileName,
        type: 'CRITICAL', 
        task: 'Add useI18n hook: const { t } = useI18n();',
        status: '❌',
        completed: false
      });
    }

    // Check RTL components
    const hasRTLImport = /import.*RTL.*from.*RTL/.test(content);
    const basicComponents = this.findBasicComponents(content);

    if (!hasRTLImport && basicComponents.length > 0) {
      tasks.push({
        file: fileName,
        type: 'HIGH',
        task: 'Add RTL imports: import { RTLView, RTLText, ... } from \'../../components/RTL\';',
        status: '❌',
        completed: false
      });
    }

    if (basicComponents.length > 0) {
      tasks.push({
        file: fileName,
        type: 'HIGH',
        task: `Replace ${basicComponents.length} basic components with RTL equivalents`,
        status: '❌',
        completed: false,
        details: basicComponents.slice(0, 3).map(comp => `${comp.component} (line ${comp.line})`).join(', ')
      });
    }

    // Check hardcoded strings
    const hardcodedStrings = this.findHardcodedStrings(content);
    if (hardcodedStrings.length > 0) {
      tasks.push({
        file: fileName,
        type: 'MEDIUM',
        task: `Replace ${hardcodedStrings.length} hardcoded strings with translation keys`,
        status: '❌',
        completed: false,
        details: hardcodedStrings.slice(0, 3).map(str => `"${str.text}" (line ${str.line})`).join(', ')
      });
    }

    // If no issues, mark as compliant
    if (tasks.length === 0) {
      tasks.push({
        file: fileName,
        type: 'SUCCESS',
        task: 'File is fully compliant',
        status: '✅',
        completed: true
      });
    }

    return tasks;
  }

  /**
   * Find basic React Native components
   */
  findBasicComponents(content) {
    const components = [];
    const regex = /<(View|Text|SafeAreaView|ScrollView|FlatList|SectionList|TextInput)\s/g;
    let match;

    while ((match = regex.exec(content)) !== null) {
      components.push({
        component: match[1],
        line: this.getLineNumber(content, match.index)
      });
    }

    return components;
  }

  /**
   * Find hardcoded strings
   */
  findHardcodedStrings(content) {
    const strings = [];
    const regex = /['"`]([A-Za-z][^'"`]*[A-Za-z])['"]/g;
    let match;

    while ((match = regex.exec(content)) !== null) {
      const text = match[1];
      if (this.isTranslatableString(text)) {
        strings.push({
          text: text,
          line: this.getLineNumber(content, match.index)
        });
      }
    }

    return strings;
  }

  /**
   * Check if string should be translated
   */
  isTranslatableString(text) {
    const nonTranslatablePatterns = [
      /^[0-9]+$/,
      /^[A-Z_]+$/,
      /^[a-z-]+$/,
      /^\w+\.\w+$/,
      /^#[0-9A-Fa-f]+$/,
      /^https?:\/\//,
      /^[A-Z]{2,}$/,
      /^\w+Icon$/
    ];

    return !nonTranslatablePatterns.some(pattern => pattern.test(text)) &&
           text.length > 2 &&
           /[A-Za-z]/.test(text);
  }

  /**
   * Get line number for character index
   */
  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  /**
   * Print comprehensive checklist
   */
  printChecklist() {
    console.log(`${colors.cyan}${colors.bright}📋 VALIDATION CHECKLIST${colors.reset}`);
    console.log(`${colors.cyan}======================\n${colors.reset}`);

    // Customer Portal
    this.printPortalChecklist('Customer Portal', this.checklist.customer);
    
    // Vendor Portal
    this.printPortalChecklist('Vendor Portal', this.checklist.vendor);
  }

  /**
   * Print checklist for specific portal
   */
  printPortalChecklist(title, tasks) {
    console.log(`${colors.magenta}${colors.bright}🏢 ${title}${colors.reset}`);
    console.log(`${colors.magenta}${'='.repeat(50)}${colors.reset}\n`);

    // Group tasks by file
    const tasksByFile = {};
    tasks.forEach(task => {
      if (!tasksByFile[task.file]) {
        tasksByFile[task.file] = [];
      }
      tasksByFile[task.file].push(task);
    });

    // Print tasks for each file
    Object.entries(tasksByFile).forEach(([fileName, fileTasks]) => {
      const compliantTasks = fileTasks.filter(t => t.completed).length;
      const totalTasks = fileTasks.length;
      const isCompliant = compliantTasks === totalTasks;

      console.log(`${colors.blue}📄 ${fileName}${colors.reset}`);
      console.log(`   Progress: ${compliantTasks}/${totalTasks} ${isCompliant ? colors.green + '✅' : colors.red + '❌'}${colors.reset}`);

      fileTasks.forEach(task => {
        const typeColor = this.getTypeColor(task.type);
        console.log(`   ${task.status} [${typeColor}${task.type}${colors.reset}] ${task.task}`);
        if (task.details) {
          console.log(`      Details: ${task.details}`);
        }
      });
      console.log('');
    });
  }

  /**
   * Get color for task type
   */
  getTypeColor(type) {
    switch (type) {
      case 'CRITICAL': return colors.red;
      case 'HIGH': return colors.yellow;
      case 'MEDIUM': return colors.blue;
      case 'SUCCESS': return colors.green;
      default: return colors.reset;
    }
  }

  /**
   * Print summary
   */
  printSummary() {
    const allTasks = [...this.checklist.customer, ...this.checklist.vendor];
    const totalTasks = allTasks.length;
    const completedTasks = allTasks.filter(t => t.completed).length;
    const criticalTasks = allTasks.filter(t => t.type === 'CRITICAL' && !t.completed).length;

    console.log(`${colors.cyan}${colors.bright}📊 SUMMARY${colors.reset}`);
    console.log(`${colors.cyan}==========\n${colors.reset}`);

    console.log(`Total Tasks: ${totalTasks}`);
    console.log(`Completed: ${colors.green}${completedTasks}${colors.reset}`);
    console.log(`Remaining: ${colors.red}${totalTasks - completedTasks}${colors.reset}`);
    console.log(`Critical: ${colors.red}${criticalTasks}${colors.reset}`);
    
    const progress = (completedTasks / totalTasks * 100).toFixed(1);
    console.log(`Progress: ${this.getProgressColor(progress)}${progress}%${colors.reset}\n`);

    // Next actions
    console.log(`${colors.blue}🚀 Next Actions:${colors.reset}`);
    if (criticalTasks > 0) {
      console.log(`   1. Address ${criticalTasks} critical tasks first`);
      console.log(`   2. Focus on translation imports and hooks`);
      console.log(`   3. Add RTL component imports`);
    } else {
      console.log(`   1. Replace basic components with RTL components`);
      console.log(`   2. Replace hardcoded strings with translation keys`);
      console.log(`   3. Test Arabic language and RTL layout`);
    }

    console.log(`\n${colors.cyan}📝 Checklist generated: ${new Date().toLocaleString()}${colors.reset}`);
  }

  /**
   * Get color for progress percentage
   */
  getProgressColor(progress) {
    if (progress >= 95) return colors.green;
    if (progress >= 75) return colors.yellow;
    return colors.red;
  }
}

// Main execution
async function main() {
  try {
    const checker = new PortalValidationChecker();
    await checker.generateChecklist();
  } catch (error) {
    console.error(`${colors.red}❌ Error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { PortalValidationChecker };
