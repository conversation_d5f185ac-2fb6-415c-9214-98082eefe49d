import React, { useState, useMemo } from 'react';
import {
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { useThemedStyles, useProducts, useVendors, useI18n } from '../../hooks';
import { Card, Button, EmptyState, StatusBadge, SwipeableRow, AdvancedFilterPanel } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Product } from '../../contexts/DataContext';

interface ProductOverviewScreenProps {
  navigation: any;
}

export const ProductOverviewScreen: React.FC<ProductOverviewScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { getAllProducts, updateProduct, deleteProduct, bulkUpdateProducts } = useProducts();
  const { getVendorById } = useVendors();
  const { t } = useI18n();
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'inventory' | 'created'>('created');

  const allProducts = getAllProducts();

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  // Enhanced filtering and sorting
  const filteredProducts = useMemo(() => {
    let filtered = allProducts.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.category.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = !selectedCategory || product.category === selectedCategory;
      const matchesStatus = !selectedStatus ||
                           (selectedStatus === 'active' && product.isActive) ||
                           (selectedStatus === 'inactive' && !product.isActive) ||
                           (selectedStatus === 'out_of_stock' && product.inventory === 0);
      
      return matchesSearch && matchesCategory && matchesStatus;
    });
    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price':
          return b.price - a.price;
        case 'inventory':
          return b.inventory - a.inventory;
        case 'created':
        default:
          return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
      }
    });

    return filtered;
  }, [allProducts, searchQuery, selectedCategory, selectedStatus, sortBy]);

  const categories = React.useMemo(() => {
    const cats = [...new Set(allProducts.map(p => p.category))];
    return cats.sort();
  }, [allProducts]);

  const statuses = ['active', 'inactive', 'out_of_stock'];

  // Bulk operations
  const handleProductSelect = (productId: string) => {
    if (isSelectionMode) {
      setSelectedProducts(prev =>
        prev.includes(productId)
          ? prev.filter(id => id !== productId)
          : [...prev, productId]
      );
    } else {
      navigation.navigate('ProductDetails', { productId });
    }
  };

  const handleLongPress = (productId: string) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedProducts([productId]);
    }
  };

  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedProducts([]);
  };

  const handleBulkDelete = () => {
    Alert.alert(
      t('admin.deleteProducts'),
      t('admin.deleteProductsConfirmation', { count: selectedProducts.length }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await Promise.all(selectedProducts.map(id => deleteProduct(id)));
              exitSelectionMode();
              Alert.alert(t('common.success'), t('admin.productsDeletedSuccess'));
            } catch (error) {
              Alert.alert(t('common.error'), t('admin.failedToDeleteProducts'));
            }
          },
        },
      ]
    );
  };

  const renderProductItem = ({ item }: { item: Product }) => {
    const vendor = getVendorById(item.vendorId);
    
    return (
      <Card style={styles.productCard} variant="elevated">
        <TouchableOpacity
          style={styles.productContent}
          onPress={() => navigation.navigate('ProductDetails', { productId: item.id })}
        >
          <RTLView style={styles.productImage}>
            <RTLIcon name="image-outline" size={32} color="#CCCCCC" />
          </RTLView>

          <RTLView style={styles.productInfo}>
            <RTLView style={styles.productHeader}>
              <RTLText style={styles.productName} numberOfLines={2}>
                {item.name}
              </RTLText>
              <StatusBadge
                status={item.inventory === 0 ? 'out_of_stock' : item.isActive ? 'active' : 'inactive'}
                variant={item.inventory === 0 ? 'error' : item.isActive ? 'success' : 'warning'}
              />
            </RTLView>

            <RTLText style={styles.productDescription} numberOfLines={2}>
              {item.description}
            </RTLText>

            <RTLView style={styles.productMeta}>
              <RTLText style={styles.vendorName}>
                {t('search.by')} {vendor?.businessName || t('search.unknownVendor')}
              </RTLText>
              <RTLText style={styles.categoryText}>
                {item.category}
              </RTLText>
            </RTLView>

            <RTLView style={styles.productFooter}>
              <RTLView style={styles.priceContainer}>
                <RTLText style={styles.productPrice}>
                  {formatCurrency(item.price)}
                </RTLText>
                {item.originalPrice && item.originalPrice > item.price && (
                  <RTLText style={styles.originalPrice}>
                    {formatCurrency(item.originalPrice)}
                  </RTLText>
                )}
              </RTLView>

              <RTLView style={styles.stockInfo}>
                <RTLIcon
                  name="cube-outline"
                  size={14}
                  color={item.inventory > 0 ? '#4CAF50' : '#F44336'}
                />
                <RTLText style={[styles.stockText, {
                  color: item.inventory > 0 ? '#4CAF50' : '#F44336'
                }]}>
                  {item.inventory} {t('products.inStock')}
                </RTLText>
              </RTLView>
            </RTLView>
          </RTLView>
        </TouchableOpacity>
      </Card>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search-outline" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search products..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        )}
      </View>

      {/* Filter Chips */}
      <View style={styles.filtersContainer}>
        <Text style={styles.filtersLabel}>Filters:</Text>
        
        {/* Category Filter */}
        <View style={styles.filterChips}>
          <TouchableOpacity
            style={[styles.filterChip, !selectedCategory && styles.filterChipActive]}
            onPress={() => setSelectedCategory(null)}
          >
            <Text style={[styles.filterChipText, !selectedCategory && styles.filterChipTextActive]}>
              All Categories
            </Text>
          </TouchableOpacity>
          
          {categories.slice(0, 3).map(category => (
            <TouchableOpacity
              key={category}
              style={[styles.filterChip, selectedCategory === category && styles.filterChipActive]}
              onPress={() => setSelectedCategory(selectedCategory === category ? null : category)}
            >
              <Text style={[styles.filterChipText, selectedCategory === category && styles.filterChipTextActive]}>
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Status Filter */}
        <View style={styles.filterChips}>
          <TouchableOpacity
            style={[styles.filterChip, !selectedStatus && styles.filterChipActive]}
            onPress={() => setSelectedStatus(null)}
          >
            <Text style={[styles.filterChipText, !selectedStatus && styles.filterChipTextActive]}>
              All Status
            </Text>
          </TouchableOpacity>
          
          {statuses.map(status => (
            <TouchableOpacity
              key={status}
              style={[styles.filterChip, selectedStatus === status && styles.filterChipActive]}
              onPress={() => setSelectedStatus(selectedStatus === status ? null : status)}
            >
              <Text style={[styles.filterChipText, selectedStatus === status && styles.filterChipTextActive]}>
                {status.replace('_', ' ')}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Results Count */}
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsText}>
          {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found
        </Text>
        
        {(searchQuery || selectedCategory || selectedStatus) && (
          <TouchableOpacity
            style={styles.clearFiltersButton}
            onPress={() => {
              setSearchQuery('');
              setSelectedCategory(null);
              setSelectedStatus(null);
            }}
          >
            <Text style={styles.clearFiltersText}>Clear All</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <FlatList
        data={filteredProducts}
        renderItem={renderProductItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={
          <EmptyState
            icon="cube-outline"
            title={t('products.noProductsFound')}
            description={
              searchQuery || selectedCategory || selectedStatus
                ? "Try adjusting your search or filters"
                : "No products have been added yet"
            }
          />
        }
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      padding: SPACING.lg,
      paddingBottom: SPACING.xl,
    },
    header: {
      marginBottom: SPACING.lg,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.md,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      marginBottom: SPACING.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    searchIcon: {
      marginRight: SPACING.sm,
    },
    searchInput: {
      flex: 1,
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    filtersContainer: {
      marginBottom: SPACING.md,
    },
    filtersLabel: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    filterChips: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: SPACING.sm,
      marginBottom: SPACING.sm,
    },
    filterChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterChipActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    filterChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textTransform: 'capitalize',
    },
    filterChipTextActive: {
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.semibold,
    },
    resultsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    resultsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    clearFiltersButton: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
    },
    clearFiltersText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    productCard: {
      marginBottom: SPACING.md,
    },
    productContent: {
      flexDirection: 'row',
      padding: SPACING.md,
    },
    productImage: {
      width: 80,
      height: 80,
      backgroundColor: colors.backgroundSecondary,
      borderRadius: BORDER_RADIUS.md,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    productInfo: {
      flex: 1,
    },
    productHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: SPACING.sm,
    },
    productName: {
      flex: 1,
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginRight: SPACING.sm,
    },
    productDescription: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
      lineHeight: 18,
    },
    productMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: SPACING.sm,
    },
    vendorName: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      fontStyle: 'italic',
    },
    categoryText: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
      textTransform: 'capitalize',
    },
    productFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productPrice: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginRight: SPACING.sm,
    },
    originalPrice: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
    },
    stockInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    stockText: {
      fontSize: FONT_SIZES.xs,
      marginLeft: SPACING.xs,
      fontWeight: FONT_WEIGHTS.medium,
    },
  });
