import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, FlatList, RefreshControl, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useVendors, useDebounce } from '../../hooks';
import { Card, StatusBadge, Button, Input, EmptyState, LoadingSpinner, SwipeableRow, SwipeActions, useEnhancedRefresh, OptimizedFlatList, useListPerformance } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { VENDOR_STATUS } from '../../constants';
import { formatDate } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor, VendorStatus } from '../../contexts/DataContext';

interface VendorManagementScreenProps {
  navigation: any;
}

export const VendorManagementScreen: React.FC<VendorManagementScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const {
    vendors,
    vendorStats,
    approveVendor,
    rejectVendor,
    searchVendors,
    getVendorsByStatus,
    isLoading,
  } = useVendors();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<VendorStatus | 'all'>('all');

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const filteredVendors = useMemo(() => {
    let result = vendors;

    // Apply status filter
    if (selectedFilter !== 'all') {
      result = getVendorsByStatus(selectedFilter);
    }

    // Apply search filter
    if (debouncedSearchQuery.trim()) {
      result = searchVendors(debouncedSearchQuery);
      if (selectedFilter !== 'all') {
        result = result.filter(vendor => vendor.status === selectedFilter);
      }
    }

    return result;
  }, [vendors, selectedFilter, debouncedSearchQuery, getVendorsByStatus, searchVendors]);

  // Enhanced refresh with haptic feedback
  const { refreshing, onRefresh } = useEnhancedRefresh(async () => {
    // Simulate data refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    // In a real app, this would refetch vendor data
  }, []);

  // Performance monitoring
  const { logScrollPerformance } = useListPerformance('VendorManagement');

  const handleApproveVendor = async (vendorId: string, vendorName: string) => {
    Alert.alert(
      t('admin.approveVendor'),
      t('admin.approveVendorConfirmation', { vendorName }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('admin.approve'),
          style: 'default',
          onPress: async () => {
            try {
              await approveVendor(vendorId);
              Alert.alert(t('common.success'), t('admin.vendorApprovedSuccess'));
            } catch (error) {
              Alert.alert(t('common.error'), t('admin.failedToApproveVendor'));
            }
          },
        },
      ]
    );
  };

  const handleRejectVendor = async (vendorId: string, vendorName: string) => {
    Alert.alert(
      t('admin.rejectVendor'),
      t('admin.rejectVendorConfirmation', { vendorName }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('admin.reject'),
          style: 'destructive',
          onPress: async () => {
            try {
              await rejectVendor(vendorId);
              Alert.alert(t('common.success'), t('admin.vendorRejectedSuccess'));
            } catch (error) {
              Alert.alert(t('common.error'), t('admin.failedToRejectVendor'));
            }
          },
        },
      ]
    );
  };

  const renderVendorItem = ({ item }: { item: Vendor }) => {
    // Define swipe actions based on vendor status
    const getSwipeActions = () => {
      if (item.status === VENDOR_STATUS.PENDING) {
        return {
          leftActions: [
            SwipeActions.approve(() => handleApproveVendor(item.id, item.businessName)),
          ],
          rightActions: [
            SwipeActions.reject(() => handleRejectVendor(item.id, item.businessName)),
          ],
        };
      }

      if (item.status === VENDOR_STATUS.APPROVED) {
        return {
          rightActions: [
            SwipeActions.view(() => {
              // Navigate to vendor details or shop
              console.log('View vendor:', item.businessName);
            }),
          ],
        };
      }

      return { leftActions: [], rightActions: [] };
    };

    const { leftActions, rightActions } = getSwipeActions();

    return (
      <SwipeableRow
        leftActions={leftActions}
        rightActions={rightActions}
        style={styles.swipeableContainer}
      >
        <Card style={styles.vendorCard} variant="elevated">
          <View style={styles.vendorHeader}>
            <View style={styles.vendorInfo}>
              <Text style={styles.businessName}>{item.businessName}</Text>
              <Text style={styles.ownerName}>by {item.ownerName}</Text>
              <Text style={styles.email}>{item.email}</Text>
            </View>
            <StatusBadge status={item.status} type="vendor" />
          </View>

          <Text style={styles.description} numberOfLines={2}>
            {item.businessDescription}
          </Text>

          <View style={styles.vendorStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{item.totalProducts}</Text>
              <Text style={styles.statLabel}>Products</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{item.totalOrders}</Text>
              <Text style={styles.statLabel}>Orders</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{item.rating.toFixed(1)}</Text>
              <Text style={styles.statLabel}>Rating</Text>
            </View>
          </View>

          <View style={styles.vendorFooter}>
            <Text style={styles.dateText}>
              Applied: {formatDate(item.createdAt)}
            </Text>

            {/* Show hint for swipe actions on pending vendors */}
            {item.status === VENDOR_STATUS.PENDING && (
              <Text style={styles.swipeHint}>
                ← Swipe to approve or reject →
              </Text>
            )}

            {/* Keep buttons as fallback for accessibility */}
            {item.status === VENDOR_STATUS.PENDING && (
              <View style={styles.actionButtons}>
                <Button
                  title="Reject"
                  onPress={() => handleRejectVendor(item.id, item.businessName)}
                  variant="outline"
                  size="small"
                  style={styles.rejectButton}
                />
                <Button
                  title="Approve"
                  onPress={() => handleApproveVendor(item.id, item.businessName)}
                  size="small"
                  style={styles.approveButton}
                />
              </View>
            )}
          </View>
        </Card>
      </SwipeableRow>
    );
  };

  const filterOptions = [
    { label: t('common.all'), value: 'all' as const },
    { label: t('orders.pending'), value: VENDOR_STATUS.PENDING },
    { label: t('admin.approved'), value: VENDOR_STATUS.APPROVED },
    { label: t('admin.rejected'), value: VENDOR_STATUS.REJECTED },
  ];

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <LoadingSpinner size="large" variant="gradient" />
      </View>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      {/* Header Stats */}
      <View style={styles.statsHeader}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{vendorStats.total}</Text>
          <Text style={styles.statTitle}>Total</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{vendorStats.pending}</Text>
          <Text style={styles.statTitle}>Pending</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{vendorStats.approved}</Text>
          <Text style={styles.statTitle}>Approved</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{vendorStats.rejected}</Text>
          <Text style={styles.statTitle}>Rejected</Text>
        </View>
      </View>

      {/* Search and Filters */}
      <View style={styles.searchContainer}>
        <Input
          placeholder="Search vendors..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon="search-outline"
          style={styles.searchInput}
          containerStyle={styles.searchInputContainer}
        />
        
        <View style={styles.filterContainer}>
          {filterOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.filterChip,
                selectedFilter === option.value && styles.filterChipActive,
              ]}
              onPress={() => setSelectedFilter(option.value)}
            >
              <Text
                style={[
                  styles.filterChipText,
                  selectedFilter === option.value && styles.filterChipTextActive,
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Vendor List */}
      <OptimizedFlatList
        data={filteredVendors}
        renderItem={renderVendorItem}
        keyExtractor={(item) => item.id}
        estimatedItemSize={200}
        enableVirtualization={true}
        enableMemoryOptimization={true}
        enableScrollOptimization={true}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        onScroll={logScrollPerformance}
        ListEmptyComponent={
          <EmptyState
            icon="storefront-outline"
            title="No vendors found"
            description={
              searchQuery
                ? "No vendors match your search criteria"
                : "No vendors have registered yet"
            }
          />
        }
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    statsHeader: {
      flexDirection: 'row',
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      backgroundColor: colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    statCard: {
      flex: 1,
      alignItems: 'center',
    },
    statNumber: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    statTitle: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginTop: SPACING.xs,
    },
    searchContainer: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      backgroundColor: colors.surface,
    },
    searchInputContainer: {
      marginBottom: SPACING.md,
    },
    searchInput: {
      marginBottom: 0,
    },
    filterContainer: {
      flexDirection: 'row',
      gap: SPACING.sm,
    },
    filterChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.round,
      backgroundColor: colors.backgroundSecondary,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterChipActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    filterChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    filterChipTextActive: {
      color: colors.textOnPrimary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    listContainer: {
      padding: SPACING.lg,
    },
    vendorCard: {
      marginBottom: SPACING.md,
    },
    vendorHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: SPACING.sm,
    },
    vendorInfo: {
      flex: 1,
      marginRight: SPACING.md,
    },
    businessName: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    ownerName: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
    email: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    description: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: FONT_SIZES.sm * 1.4,
      marginBottom: SPACING.md,
    },
    vendorStats: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: SPACING.md,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderColor: colors.borderLight,
      marginBottom: SPACING.md,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
    },
    statLabel: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginTop: SPACING.xs,
    },
    vendorFooter: {
      flexDirection: 'column',
      gap: SPACING.sm,
    },
    dateText: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
    swipeHint: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      textAlign: 'center',
      fontStyle: 'italic',
      opacity: 0.8,
    },
    actionButtons: {
      flexDirection: 'row',
      gap: SPACING.sm,
      justifyContent: 'flex-end',
    },
    rejectButton: {
      borderColor: colors.error,
    },
    approveButton: {
      minWidth: 80,
    },
    swipeableContainer: {
      marginBottom: SPACING.md,
    },
  });
