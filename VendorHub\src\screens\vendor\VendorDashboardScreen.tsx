import React from 'react';
import {
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles, useAuth, useVendors, useProducts, useOrders, useI18n } from '../../hooks';
import { RTLView, RTLText, RTLScrollView, RTLIcon } from '../../components/RTL';
import { Card, Button, StatisticsCounter, EmptyState, Chart, StatisticsCard, NotificationCenter, NotificationBadge, VendorAnalyticsChart } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS,
  ICON_SIZES,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface VendorDashboardScreenProps {
  navigation: any;
}

export const VendorDashboardScreen: React.FC<VendorDashboardScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user, logout } = useAuth();
  const { getVendorById } = useVendors();
  const { getProductsByVendor } = useProducts();
  const { getOrdersByVendor } = useOrders();
  const { t } = useI18n();
  const [refreshing, setRefreshing] = React.useState(false);
  const [showNotifications, setShowNotifications] = React.useState(false);

  const vendor = user ? getVendorById(user.id) : null;
  const vendorProducts = vendor ? getProductsByVendor(vendor.id) : [];
  const vendorOrders = vendor ? getOrdersByVendor(vendor.id) : [];

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleLogout = async () => {
    await logout();
  };

  // Calculate statistics
  const totalProducts = vendorProducts.length;
  const activeProducts = vendorProducts.filter(p => p.status === 'active').length;
  const totalOrders = vendorOrders.length;
  const pendingOrders = vendorOrders.filter(o => o.status === 'pending').length;
  const totalRevenue = vendorOrders
    .filter(o => o.status === 'completed')
    .reduce((sum, order) => sum + order.totalAmount, 0);

  const quickActions = [
    {
      title: t('vendor.addProduct'),
      subtitle: t('vendor.createNewProduct'),
      icon: 'add-circle-outline',
      color: '#4CAF50',
      onPress: () => navigation.navigate('AddProduct'),
    },
    {
      title: t('vendor.manageProducts'),
      subtitle: `${totalProducts} ${t('admin.productsText')}`,
      icon: 'cube-outline',
      color: '#2196F3',
      onPress: () => navigation.navigate('Products'),
    },
    {
      title: t('vendor.viewOrders'),
      subtitle: `${pendingOrders} ${t('orders.pending')}`,
      icon: 'receipt-outline',
      color: '#FF9800',
      onPress: () => navigation.navigate('Orders'),
    },
    {
      title: t('vendor.shopSettings'),
      subtitle: t('vendor.customizeYourShop'),
      icon: 'settings-outline',
      color: '#9C27B0',
      onPress: () => navigation.navigate('ShopSettings'),
    },
  ];

  const recentOrders = vendorOrders.slice(0, 3);

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <LinearGradient colors={GRADIENTS.primary} style={styles.header}>
          <RTLView style={styles.headerContent}>
            <RTLView style={styles.headerLeft}>
              <RTLText style={styles.welcomeText}>{t('common.welcomeBack')}</RTLText>
              <RTLText style={styles.vendorName}>{vendor?.businessName || t('auth.vendor')}</RTLText>
            </RTLView>
            <RTLView style={styles.headerRight}>
              <NotificationBadge
                onPress={() => setShowNotifications(true)}
                style={styles.notificationButton}
              />
              <TouchableOpacity onPress={handleLogout} style={styles.headerButton}>
                <RTLIcon name="log-out-outline" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </RTLView>
          </RTLView>
        </LinearGradient>

        {/* Statistics Cards */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('vendor.overview')}</RTLText>
          <RTLView style={styles.statsGrid}>
            <Card style={styles.statCard} variant="elevated">
              <RTLView style={styles.statContent}>
                <RTLView style={[styles.statIcon, { backgroundColor: '#4CAF50' }]}>
                  <RTLIcon name="trending-up" size={24} color="#FFFFFF" />
                </RTLView>
                <RTLView style={styles.statInfo}>
                  <StatisticsCounter
                    value={totalRevenue}
                    label={t('vendor.totalRevenue')}
                    format="currency"
                    style={styles.statValue}
                  />
                </RTLView>
              </RTLView>
            </Card>

            <Card style={styles.statCard} variant="elevated">
              <RTLView style={styles.statContent}>
                <RTLView style={[styles.statIcon, { backgroundColor: '#2196F3' }]}>
                  <RTLIcon name="cube" size={24} color="#FFFFFF" />
                </RTLView>
                <RTLView style={styles.statInfo}>
                  <StatisticsCounter
                    value={totalProducts}
                    label={t('vendor.totalProducts')}
                    style={styles.statValue}
                  />
                </RTLView>
              </RTLView>
            </Card>

            <Card style={styles.statCard} variant="elevated">
              <RTLView style={styles.statContent}>
                <RTLView style={[styles.statIcon, { backgroundColor: '#FF9800' }]}>
                  <RTLIcon name="receipt" size={24} color="#FFFFFF" />
                </RTLView>
                <RTLView style={styles.statInfo}>
                  <StatisticsCounter
                    value={totalOrders}
                    label={t('vendor.totalOrders')}
                    style={styles.statValue}
                  />
                </RTLView>
              </RTLView>
            </Card>

            <Card style={styles.statCard} variant="elevated">
              <RTLView style={styles.statContent}>
                <RTLView style={[styles.statIcon, { backgroundColor: '#E91E63' }]}>
                  <RTLIcon name="time" size={24} color="#FFFFFF" />
                </RTLView>
                <RTLView style={styles.statInfo}>
                  <StatisticsCounter
                    value={pendingOrders}
                    label={t('vendor.pendingOrders')}
                    style={styles.statValue}
                  />
                </RTLView>
              </RTLView>
            </Card>
          </RTLView>
        </RTLView>

        {/* Analytics Charts */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>Analytics</RTLText>

          {/* Revenue Chart */}
          <Chart
            type="line"
            title="Revenue Trend"
            subtitle="Last 30 days"
            data={{
              labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
              datasets: [{
                data: [
                  Math.floor(totalRevenue * 0.2),
                  Math.floor(totalRevenue * 0.3),
                  Math.floor(totalRevenue * 0.25),
                  Math.floor(totalRevenue * 0.25),
                ],
                color: (opacity = 1) => `rgba(102, 126, 234, ${opacity})`,
                strokeWidth: 3,
              }],
            }}
            height={200}
          />

          {/* Product Performance */}
          <Chart
            type="bar"
            title="Product Sales"
            subtitle="Top performing products"
            data={{
              labels: vendorProducts.slice(0, 5).map(p => p.name.substring(0, 8) + '...'),
              datasets: [{
                data: vendorProducts.slice(0, 5).map(p => p.reviewCount || Math.floor(Math.random() * 50) + 10),
              }],
            }}
            height={200}
          />

          {/* Performance Metrics */}
          <RTLView style={styles.metricsRow}>
            <StatisticsCard
              title={t('vendor.averageOrderValue')}
              value={totalRevenue / Math.max(totalOrders, 1)}
              format="currency"
              icon="cash-outline"
              trend={{
                value: 12.5,
                isPositive: true,
                period: t('admin.vsLastMonth'),
              }}
              style={styles.metricCard}
            />
            <StatisticsCard
              title={t('vendor.conversionRate')}
              value={8.7}
              format="percentage"
              icon="trending-up-outline"
              trend={{
                value: 3.2,
                isPositive: true,
                period: t('admin.vsLastMonth'),
              }}
              style={styles.metricCard}
            />
          </View>

          <RTLView style={styles.metricsRow}>
            <StatisticsCard
              title={t('vendor.activeProducts')}
              value={vendorProducts.filter(p => p.isActive).length}
              format="number"
              icon="cube-outline"
              trend={{
                value: 5.1,
                isPositive: true,
                period: t('admin.vsLastMonth'),
              }}
              style={styles.metricCard}
            />
            <StatisticsCard
              title={t('vendor.customerSatisfaction')}
              value={vendor?.rating || 4.5}
              subtitle={t('admin.outOfFive')}
              icon="star-outline"
              trend={{
                value: 0.2,
                isPositive: true,
                period: t('admin.vsLastMonth'),
              }}
              style={styles.metricCard}
            />
          </RTLView>
        </RTLView>

        {/* Analytics Chart */}
        <RTLView style={styles.section}>
          <VendorAnalyticsChart
            orders={vendorOrders}
            products={vendorProducts}
            style={styles.analyticsChart}
          />
        </RTLView>

        {/* Quick Actions */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('vendor.quickActions')}</RTLText>
          <RTLView style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={styles.quickActionCard}
                onPress={action.onPress}
              >
                <Card style={styles.actionCard} variant="elevated">
                  <RTLView style={[styles.actionIcon, { backgroundColor: action.color }]}>
                    <RTLIcon name={action.icon as any} size={24} color="#FFFFFF" />
                  </RTLView>
                  <RTLText style={styles.actionTitle}>{action.title}</RTLText>
                  <RTLText style={styles.actionSubtitle}>{action.subtitle}</RTLText>
                </Card>
              </TouchableOpacity>
            ))}
          </RTLView>
        </RTLView>

        {/* Recent Orders */}
        <RTLView style={styles.section}>
          <RTLView style={styles.sectionHeader}>
            <RTLText style={styles.sectionTitle}>{t('vendor.recentOrders')}</RTLText>
            <TouchableOpacity onPress={() => navigation.navigate('Orders')}>
              <RTLText style={styles.seeAllText}>{t('common.seeAll')}</RTLText>
            </TouchableOpacity>
          </RTLView>
          
          {recentOrders.length > 0 ? (
            <RTLView style={styles.ordersList}>
              {recentOrders.map((order) => (
                <Card key={order.id} style={styles.orderCard} variant="elevated">
                  <RTLView style={styles.orderHeader}>
                    <RTLText style={styles.orderId}>Order #{order.id}</RTLText>
                    <RTLView style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
                      <RTLText style={styles.statusText}>{order.status.toUpperCase()}</RTLText>
                    </RTLView>
                  </RTLView>
                  <RTLText style={styles.orderCustomer}>{order.customerName}</RTLText>
                  <RTLView style={styles.orderFooter}>
                    <RTLText style={styles.orderTotal}>{formatCurrency(order.totalAmount)}</RTLText>
                    <RTLText style={styles.orderDate}>{new Date(order.createdAt).toLocaleDateString()}</RTLText>
                  </RTLView>
                </Card>
              ))}
            </RTLView>
          ) : (
            <EmptyState
              icon="receipt-outline"
              title="No recent orders"
              description="Orders will appear here when customers make purchases"
              size="small"
            />
          )}
        </RTLView>

        {/* Shop Performance */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('vendor.shopPerformance')}</RTLText>
          <Card style={styles.performanceCard} variant="elevated">
            <RTLView style={styles.performanceRow}>
              <RTLText style={styles.performanceLabel}>{t('vendor.activeProducts')}</RTLText>
              <RTLText style={styles.performanceValue}>{activeProducts}/{totalProducts}</RTLText>
            </RTLView>
            <RTLView style={styles.performanceRow}>
              <RTLText style={styles.performanceLabel}>{t('vendor.orderCompletionRate')}</RTLText>
              <RTLText style={styles.performanceValue}>
                {totalOrders > 0 ? Math.round((vendorOrders.filter(o => o.status === 'completed').length / totalOrders) * 100) : 0}%
              </RTLText>
            </RTLView>
            <RTLView style={styles.performanceRow}>
              <RTLText style={styles.performanceLabel}>{t('vendor.averageOrderValue')}</RTLText>
              <RTLText style={styles.performanceValue}>
                {totalOrders > 0 ? formatCurrency(totalRevenue / totalOrders) : formatCurrency(0)}
              </RTLText>
            </RTLView>
          </Card>
        </RTLView>
      </RTLScrollView>

      {/* Notification Center */}
      <NotificationCenter
        visible={showNotifications}
        onClose={() => setShowNotifications(false)}
        onNotificationPress={(notification) => {
          // Handle notification press based on type
          setShowNotifications(false);
          // Add navigation logic here based on notification type
        }}
      />
    </RTLSafeAreaView>
  );
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return '#FF9800';
    case 'processing': return '#2196F3';
    case 'shipped': return '#9C27B0';
    case 'completed': return '#4CAF50';
    case 'cancelled': return '#F44336';
    default: return '#757575';
  }
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: SPACING.xl,
    },
    header: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.lg,
      marginBottom: SPACING.lg,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerLeft: {
      flex: 1,
    },
    headerRight: {
      flexDirection: 'row',
      gap: SPACING.sm,
    },
    headerButton: {
      padding: SPACING.sm,
    },
    notificationButton: {
      marginRight: SPACING.sm,
    },
    welcomeText: {
      fontSize: FONT_SIZES.sm,
      color: '#FFFFFF',
      opacity: 0.8,
    },
    vendorName: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
      marginTop: SPACING.xs,
    },
    section: {
      paddingHorizontal: SPACING.lg,
      marginBottom: SPACING.lg,
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.md,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    seeAllText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    statsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: SPACING.md,
    },
    statCard: {
      flex: 1,
      minWidth: '45%',
    },
    statContent: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: SPACING.md,
    },
    statIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    statInfo: {
      flex: 1,
    },
    statValue: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    statLabel: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginTop: SPACING.xs,
    },
    quickActionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: SPACING.md,
    },
    quickActionCard: {
      flex: 1,
      minWidth: '45%',
    },
    actionCard: {
      padding: SPACING.md,
      alignItems: 'center',
    },
    actionIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    actionTitle: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      textAlign: 'center',
      marginBottom: SPACING.xs,
    },
    actionSubtitle: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    ordersList: {
      gap: SPACING.md,
    },
    orderCard: {
      padding: SPACING.md,
    },
    orderHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    orderId: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
    },
    statusBadge: {
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: 12,
    },
    statusText: {
      fontSize: FONT_SIZES.xs,
      fontWeight: FONT_WEIGHTS.semibold,
      color: '#FFFFFF',
    },
    orderCustomer: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.sm,
    },
    orderFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    orderTotal: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
    },
    orderDate: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
    performanceCard: {
      padding: SPACING.md,
    },
    performanceRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    performanceLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    performanceValue: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
    },
    metricsRow: {
      flexDirection: 'row',
      marginBottom: SPACING.md,
      gap: SPACING.md,
    },
    metricCard: {
      flex: 1,
    },
    analyticsChart: {
      marginBottom: SPACING.lg,
    },
  });
